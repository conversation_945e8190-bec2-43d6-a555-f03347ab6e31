es_name: "ARS_native"
problem_type: "brax"
env_name: "ant"
normalize: true
es_config:
  pop_size: 1024
  elite_ratio: 0.05
  init_stdev: 0.05
  decay_stdev: 0.999
  limit_stdev: 0.001
  optimizer: "adam"
  optimizer_config:
    lrate_init: 0.01
    lrate_decay: 0.999
    lrate_limit: 0.001
    beta_1: 0.99
    beta_2: 0.999
    eps: 1e-08
num_tests: 128
n_repeats: 16
max_iter: 700
test_interval: 100
log_interval: 20
seed: 42  
gpu_id: 0
debug: false

