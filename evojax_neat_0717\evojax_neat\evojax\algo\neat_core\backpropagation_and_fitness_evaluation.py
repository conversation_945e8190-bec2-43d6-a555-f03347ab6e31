import jax
import jax.numpy as jnp
import optax
import chex
from typing import Callable, Optional, Tuple

from .network import Network, ActivationState, NetworkBatch, extract_single_network, reconstruct_network_batch
from .config.base import BackpropConfig, FitnessConfig
from .constants import NODE_UNUSED

import logging
logger = logging.getLogger(__name__)

def evaluate_fitness(
    network: Network,
    inputs: jnp.ndarray,  # Only inputs are passed explicitly
    fitness_fn: Callable,  # Handles both inputs and expected outputs
    config: FitnessConfig
) -> dict:
    """
    Evaluates the fitness of a network on a given task, with optional structural regularization.
    If config.fitness.connection_cost or config.fitness.node_cost is nonzero, applies regularization penalties.
    Returns a dict with regularized_fitness, raw_fitness, penalties, and network metrics.
    """
    def calculate_network_metrics(network: Network) -> dict:
        num_connections = jnp.sum(network.enabled)
        num_nodes = jnp.sum(network.node_types != NODE_UNUSED)
        weights = network.connections[:, 2]
        return {
            'num_connections': num_connections,
            'num_nodes': num_nodes,
            'weight_l1': jnp.sum(jnp.abs(weights)),
            'weight_l2': jnp.sum(jnp.square(weights)),
            'weight_mean': jnp.mean(jnp.abs(weights)),
        }

    connection_cost = config.connection_cost
    node_cost = config.node_cost
    
    activation_state = ActivationState(
        node_depths=jnp.full(network.max_nodes, -1, dtype=jnp.int32),
        outdated_depths=True
    )
    raw_fitness = fitness_fn(network, activation_state, inputs)
    metrics = calculate_network_metrics(network)
    connection_penalty = connection_cost * metrics['num_connections']
    node_penalty = node_cost * metrics['num_nodes']
    regularization_loss = connection_penalty + node_penalty
    final_fitness = raw_fitness - regularization_loss
    
    return {
        'regularized_fitness': final_fitness,
        'raw_fitness': raw_fitness,
        'connection_penalty': connection_penalty,
        'node_penalty': node_penalty,
        **metrics
    }

def create_optimizer(
    config: BackpropConfig
) -> optax.GradientTransformation:
    """Create an Optax optimizer using parameters from the config."""
    learning_rate = config.learning_rate
    gradient_clip = config.gradient_clip
    max_errors = config.max_errors
    
    return optax.chain(
        optax.clip_by_global_norm(gradient_clip),
        optax.apply_if_finite(
            inner=optax.adam(learning_rate=learning_rate),
            max_consecutive_errors=max_errors
        )
    )

def train_batch(
    batch_idx: int,
    carry: Tuple[jnp.ndarray, optax.OptState],
    padded_inputs: jnp.ndarray,
    padded_targets: jnp.ndarray,
    batch_size: int,
    optimizer: optax.GradientTransformation,
    enabled_mask: jnp.ndarray,
    activation_state: ActivationState,
    loss_fn: Callable,
    network: Network
) -> Tuple[jnp.ndarray, optax.OptState]:
    """
    Perform a single mini-batch update step for network weights using backpropagation.

    Args:
        batch_idx: Index of the current batch.
        carry: Tuple of (weights, opt_state) for the optimizer.
        padded_inputs: Input data, shape [total_batches * batch_size, num_inputs].
        padded_targets: Target data, shape [total_batches * batch_size, num_outputs].
        batch_size: Number of samples per batch.
        optimizer: Optax optimizer transformation.
        enabled_mask: Boolean mask for enabled connections.
        activation_state: ActivationState for the network.
        loss_fn: Loss function to minimize.
        network: Network instance being trained.

    Returns:
        Tuple of (new_weights, new_opt_state) after the update step.
    """
    weights, opt_state = carry
    start_idx = batch_idx * batch_size
    batch_inputs = jax.lax.dynamic_slice(
        padded_inputs, (start_idx, 0), (batch_size, padded_inputs.shape[1])
    )
    batch_targets = jax.lax.dynamic_slice(
        padded_targets, (start_idx, 0), (batch_size, padded_targets.shape[1])
    )
    def batch_loss(weights, batch_inputs, batch_targets):
        masked_weights = jnp.where(enabled_mask, weights, network.connections[:, 2])
        updated_connections = network.connections.at[:, 2].set(masked_weights)
        updated_network = network.replace(connections=updated_connections)
        predictions, _ = updated_network.forward(batch_inputs, activation_state)
        return loss_fn(predictions, batch_targets)
    value_and_grad_fn = jax.value_and_grad(batch_loss, argnums=0)
    loss, grads = value_and_grad_fn(weights, batch_inputs, batch_targets)
    updates, new_opt_state = optimizer.update(grads, opt_state)
    new_weights = optax.apply_updates(weights, updates)
    return (new_weights, new_opt_state)

# JIT-compile train_batch with static_argnames for clarity and future-proofing.
# Only pass Python objects as static: batch_size, optimizer, loss_fn
# enabled_mask, activation_state, and network are JAX arrays/pytrees and must NOT be static.
train_batch_jit = jax.jit(
    train_batch,
    static_argnames=['batch_size', 'optimizer', 'loss_fn']
)

def train_network(
    network: Network,
    inputs: jnp.ndarray,
    targets: jnp.ndarray,
    loss_fn: Callable,
    key: chex.PRNGKey,
    config: BackpropConfig,
    optimizer: optax.GradientTransformation,
    opt_state: optax.OptState
) -> Network:
    """
    Perform the training loop for a single network using mini-batch backpropagation.

    Args:
        network: The Network instance to optimize.
        inputs: Input data, shape [num_samples, num_inputs].
        targets: Target output data, shape [num_samples, num_outputs].
        loss_fn: Loss function to minimize. Should take (predictions, targets) and return a scalar loss.
        key: JAX PRNGKey for shuffling data each epoch.
        config: BackpropConfig containing training hyperparameters (num_epochs, batch_size, max_weight, etc.).
        optimizer: Optax optimizer transformation.
        opt_state: Initial optimizer state for the network's weights.

    Returns:
        Network: A new Network instance with optimized weights (connection weights updated).
    """
    num_epochs = config.num_epochs
    batch_size = config.batch_size
    max_weight = config.max_weight
    
    # Create activation state for this network
    activation_state = ActivationState(
        node_depths=jnp.full(network.max_nodes, -1, dtype=jnp.int32),
        outdated_depths=True
    )

    initial_weights = network.connections[:, 2]
    enabled_mask = network.enabled

    def train_epoch(carry, epoch_key):
        weights, opt_state = carry
        num_samples = inputs.shape[0]
        num_batches = (num_samples + batch_size - 1) // batch_size

        shuffled_indices = jax.random.permutation(epoch_key, num_samples)
        epoch_inputs = inputs[shuffled_indices]
        epoch_targets = targets[shuffled_indices]

        padded_size = batch_size * num_batches
        padded_inputs = jnp.pad(
            epoch_inputs, ((0, padded_size - num_samples), (0, 0)), mode='constant'
        )
        padded_targets = jnp.pad(
            epoch_targets, ((0, padded_size - num_samples), (0, 0)), mode='constant'
        )

        def batch_loop(batch_idx, carry):
            return train_batch_jit(
                batch_idx,
                carry,
                padded_inputs,
                padded_targets,
                batch_size,
                optimizer,
                enabled_mask,
                activation_state,
                loss_fn,
                network
            )

        final_weights, final_opt_state = jax.lax.fori_loop(
            0, num_batches, batch_loop, (weights, opt_state)
        )

        return (final_weights, final_opt_state), None

    epoch_keys = jax.random.split(key, num_epochs)

    final_weights, _ = jax.lax.scan(
        train_epoch, (initial_weights, opt_state), epoch_keys
    )

    final_weights = jnp.clip(final_weights[0], -max_weight, max_weight)

    optimized_connections = network.connections.at[:, 2].set(
        jnp.where(enabled_mask, final_weights, network.connections[:, 2])
    )
    return network.replace(connections=optimized_connections)

def optimize_weights_with_backprop(
    network: Network,
    inputs: jnp.ndarray,
    targets: jnp.ndarray,
    loss_fn: Callable,
    key: chex.PRNGKey,
    config: BackpropConfig
) -> Network:
    """
    Optimize the weights of a single network using mini-batch backpropagation.

    This function performs supervised learning on the provided network by updating its connection
    weights to minimize the specified loss function over the given input-target pairs. The optimization
    uses the Adam optimizer (with gradient clipping and NaN/Inf protection) and supports mini-batch
    training with shuffling each epoch.

    Args:
        network: The Network instance to optimize.
        inputs: Input data, shape [num_samples, num_inputs].
        targets: Target output data, shape [num_samples, num_outputs].
        loss_fn: Loss function to minimize. Should take (predictions, targets) and return a scalar loss.
        key: JAX PRNGKey for shuffling data each epoch.
        config: BackpropConfig object containing backpropagation parameters (backprop_learning_rate, num_epochs, batch_size, etc.).

    Returns:
        Network: A new Network instance with optimized weights (connection weights updated).
    """
    backprop_rounds = config.rounds

    def body_fn(round_idx, net):
        round_key = jax.random.fold_in(key, round_idx)
        optimizer = create_optimizer(config)
        opt_state = optimizer.init(net.connections[:, 2])
        return train_network(
            network=net,
            inputs=inputs,
            targets=targets,
            loss_fn=loss_fn,
            key=round_key,
            config=config,
            optimizer=optimizer,
            opt_state=opt_state
        )
    return jax.lax.fori_loop(0, int(backprop_rounds), body_fn, network)



def evaluate_networks(
    networks: NetworkBatch,
    inputs: jnp.ndarray,
    targets: Optional[jnp.ndarray],
    fitness_fn: Callable,
    key: chex.PRNGKey,
    backprop_config: BackpropConfig,
    fitness_config: FitnessConfig
) -> Tuple[NetworkBatch, jnp.ndarray]:
    """
    Evaluates a population of networks, optionally applying backpropagation first.

    The backpropagation decision is made at the population level - either all networks
    undergo backpropagation or none do, based on the backprop_config.enabled flag.

    Args:
        networks: JAX pytree of Network objects with a leading batch dimension.
        inputs: Input data, shape [num_samples, num_inputs].
        targets: Target outputs, shape [num_samples, num_outputs], required if backprop is enabled.
        fitness_fn: Task-specific evaluation function that returns a fitness score (higher is better).
                   If backpropagation is enabled, the negative of this function's output is used as the loss.
        key: JAX random key for stochastic operations.
        backprop_config: BackpropConfig object containing backpropagation parameters.
        fitness_config: FitnessConfig object containing fitness evaluation parameters.

    Returns:
        Tuple of (optimized_networks, fitness_scores) where:
        - optimized_networks: JAX pytree of networks (optimized if backprop was enabled)
        - fitness_scores: Array of fitness values for each network
    """
    # Input validation
    if backprop_config.enabled and targets is None:
        raise ValueError("Targets must be provided when backpropagation is enabled")

    # Helper function to create individual networks from batch
    def extract_network(i):
        return extract_single_network(networks, i)

    if not backprop_config.enabled:
        # Simple evaluation without backpropagation
        def eval_network(i) -> float:
            network = extract_network(i)
            return evaluate_fitness(network, inputs, fitness_fn, fitness_config)['regularized_fitness']

        batch_size = networks.node_types.shape[0]
        fitness_scores = jax.vmap(eval_network)(jnp.arange(batch_size))
        return networks, fitness_scores

    # Backpropagation path
    batch_size = networks.node_types.shape[0]
    population_keys = jax.random.split(key, batch_size)
    
    # Function to optimize a single network
    def optimize_single(i, key):
        network = extract_network(i)
        return optimize_weights_with_backprop(
            network, inputs, targets, lambda x, y: -fitness_fn(x, y), key, backprop_config
        )
    
    # Optimize networks with backprop
    optimized_networks = jax.vmap(optimize_single)(jnp.arange(batch_size), population_keys)
    
    # Reconstruct NetworkBatch from optimized individual networks
    optimized_batch = reconstruct_network_batch(optimized_networks)
    
    # Evaluate final fitness
    def eval_optimized_network(i) -> float:
        network = extract_single_network(optimized_batch, i)
        return evaluate_fitness(network, inputs, fitness_fn, fitness_config)['regularized_fitness']
    
    fitness_scores = jax.vmap(eval_optimized_network)(jnp.arange(batch_size))
    
    return optimized_batch, fitness_scores