import matplotlib.pyplot as plt
import numpy as np
import jax.numpy as jnp
from typing import Dict, Optional, Tuple, Callable, Any
import os
from matplotlib.figure import Figure
from matplotlib.axes import Axes
import jax

# Optional graphviz import
try:
    import graphviz
    GRAPHVIZ_AVAILABLE = True
except ImportError:
    GRAPHVIZ_AVAILABLE = False
    graphviz = None

from .network import NetworkBatch, Network, extract_single_network
from .species import SpeciesState
from .constants import DEFAULT_NETWORK_TITLE, DEFAULT_EVOLUTION_TITLE, DEFAULT_PLOT_FORMAT # Added import

def plot_fitness_history(
    fitness_history: jnp.ndarray,
    species_counts: Optional[jnp.ndarray] = None,
    title: str = DEFAULT_EVOLUTION_TITLE, # Use constant
    save_path: Optional[str] = None,
    show: bool = False
) -> Tuple[Figure, Axes]:
    """
    Plot fitness history and optionally species counts over generations.
    
    Args:
        fitness_history: Array of best fitness values per generation
        species_counts: Optional array of species counts per generation
        title: Title for the plot
        save_path: Optional path to save the plot
        show: Whether to display the plot
        
    Returns:
        Tuple of (Figure, Axes) objects
    """
    fig, ax1 = plt.subplots(figsize=(10, 6))
    
    # Plot fitness
    generations = np.arange(len(fitness_history))
    ax1.plot(generations, fitness_history, 'b-', linewidth=2, label='Best Fitness')
    ax1.set_xlabel('Generation')
    ax1.set_ylabel('Fitness', color='b')
    ax1.tick_params(axis='y', labelcolor='b')
    ax1.grid(True, alpha=0.3)
    
    # Plot species count on secondary y-axis if provided
    if species_counts is not None:
        ax2 = ax1.twinx()
        ax2.plot(generations, species_counts, 'r-', linewidth=2, label='Species Count')
        ax2.set_ylabel('Number of Species', color='r')
        ax2.tick_params(axis='y', labelcolor='r')
        
        # Add legend for both lines
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    else:
        ax1.legend()
    
    plt.title(title)
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
    
    if show:
        plt.show()
    else:
        plt.close()
    
    return fig, ax1

def visualize_network(
    network: Network,
    title: str = DEFAULT_NETWORK_TITLE,
    node_colors: Optional[Dict[str, str]] = None,
    show_weights: bool = True,
    save_path: Optional[str] = None,
    format: str = DEFAULT_PLOT_FORMAT # Use constant
) -> Optional[object]:
    """
    Visualize a NEAT network using Graphviz.
    
    Args:
        network: Network to visualize
        title: Title for the graph
        node_colors: Dictionary mapping node types to colors
        show_weights: Whether to show connection weights
        save_path: Path to save the visualization (without extension)
        format: Output format (png, pdf, svg)
        
    Returns:
        Graphviz Digraph object if available, None otherwise
    """
    if not GRAPHVIZ_AVAILABLE:
        print("⚠️  Graphviz not available - skipping network visualization")
        print("   Install with: sudo apt-get install graphviz (or conda install graphviz)")
        return None
    
    try:
        if node_colors is None:
            node_colors = {
                'input': '#ADD8E6',    # Light blue
                'hidden': '#D3D3D3',   # Light gray
                'output': '#90EE90'    # Light green
            }
        
        # Create graph
        dot = graphviz.Digraph(comment=title) # title is already using the constant via default arg
        dot.attr(rankdir="LR")  # Left to right layout
        dot.attr("node", shape="circle")
        
        # Add nodes - NEAT indexing: 0=bias, 1..num_inputs=inputs, (num_inputs+1)..(num_inputs+num_outputs)=outputs
        bias_node = 0
        input_nodes = list(range(1, network.num_inputs + 1))  # Nodes 1 to num_inputs
        output_nodes = list(range(network.num_inputs + 1, network.num_inputs + network.num_outputs + 1))  # After inputs
        hidden_nodes = set()
        
        # Find hidden nodes from connections
        for i in range(len(network.connections)):
            if bool(network.enabled[i]):  # Convert JAX bool to Python bool
                src, dst, _, _ = network.connections[i]
                # Convert JAX arrays to Python ints for hashability
                src, dst = int(src), int(dst)
                # Hidden nodes are any used nodes that aren't bias, input, or output
                all_fixed_nodes = {bias_node} | set(input_nodes) | set(output_nodes)
                if src not in all_fixed_nodes:
                    hidden_nodes.add(src)
                if dst not in all_fixed_nodes:
                    hidden_nodes.add(dst)
        
        # Add bias node
        dot.node(
            f"node_{bias_node}",
            "Bias",
            style="filled",
            fillcolor="#FFD700",  # Gold color for bias
            shape="box"
        )
        
        # Add input nodes
        with dot.subgraph(name="cluster_inputs") as inputs:
            inputs.attr(label="Inputs", style="rounded")
            for i, idx in enumerate(input_nodes):
                inputs.node(
                    f"node_{idx}",
                    f"In {i}",  # Label as In 0, In 1, etc.
                    style="filled",
                    fillcolor=node_colors['input']
                )
        
        # Add hidden nodes
        for idx in sorted(hidden_nodes):  # Sort for consistent ordering
            dot.node(
                f"node_{idx}",
                f"H {idx}",
                style="filled",
                fillcolor=node_colors['hidden']
            )
        
        # Add output nodes
        with dot.subgraph(name="cluster_outputs") as outputs:
            outputs.attr(label="Outputs", style="rounded")
            for i, idx in enumerate(output_nodes):
                outputs.node(
                    f"node_{idx}",
                    f"Out {i}",
                    style="filled",
                    fillcolor=node_colors['output']
                )
        
        # Add connections
        for i in range(len(network.connections)):
            if bool(network.enabled[i]):  # Convert JAX bool to Python bool
                src, dst, weight, _ = network.connections[i]
                # Convert JAX arrays to Python values for string formatting and comparisons
                src, dst, weight = int(src), int(dst), float(weight)
                
                # Edge attributes
                edge_attrs = {
                    "penwidth": f"{min(abs(weight) * 2, 5):.2f}",
                    "arrowsize": "0.5",
                }
                
                # Add weight label if requested
                if show_weights:
                    edge_attrs["label"] = f" {weight:.2f}"
                
                # Different styles for positive/negative weights
                if weight > 0:
                    edge_attrs["color"] = "green"
                else:
                    edge_attrs["color"] = "red"
                
                # Add the edge
                dot.edge(
                    f"node_{src}",
                    f"node_{dst}",
                    **edge_attrs
                )
        
        # Save if path provided
        if save_path:
            dot.format = format # format is already using the constant via default arg
            dot.render(save_path, cleanup=True)
        
        return dot
        
    except Exception as e:
        print(f"⚠️  Error creating network visualization: {e}")
        print("   Network visualization skipped")
        return None

def create_visualization_hook(
    output_dir: str,
    plot_interval: int = 10,
    network_interval: int = 50,
    plot_species: bool = True,
    max_networks_per_gen: int = 3,
    show_weights: bool = True,
    format: str = DEFAULT_PLOT_FORMAT # Use constant
) -> Callable:
    """
    Create a visualization hook function that can be passed to run_evolution.
    
    Args:
        output_dir: Directory to save visualizations
        plot_interval: Interval (in generations) for plotting fitness history
        network_interval: Interval for visualizing network structures
        plot_species: Whether to plot species counts
        max_networks_per_gen: Maximum number of networks to visualize per generation
        show_weights: Whether to show connection weights in network visualizations
        format: Output format for network visualizations (png, pdf, svg)
        
    Returns:
        Hook function that can be passed to run_evolution
    """
    os.makedirs(output_dir, exist_ok=True)
    fitness_history = []
    species_counts = []
    
    def hook(
        generation: int,
        networks: NetworkBatch,
        species_state: SpeciesState,
        fitnesses: jnp.ndarray,
        best_fitness: float,
        best_idx: int
    ):
        # Update histories
        fitness_history.append(float(best_fitness))
        # Compute species sizes from member masks
        species_sizes = jnp.sum(species_state.member_masks, axis=1)
        species_count = jnp.sum(species_sizes > 0)
        species_counts.append(int(species_count))
        
        # Plot fitness history
        if generation % plot_interval == 0 or generation == 0:
            plot_fitness_history(
                fitness_history=jnp.array(fitness_history),
                species_counts=jnp.array(species_counts) if plot_species else None,
                title=f"NEAT Evolution Progress (Generation {generation})",
                save_path=os.path.join(output_dir, f"fitness_gen{generation:04d}.png")
            )
        
        # Visualize networks
        if generation % network_interval == 0 or generation == 0:
            # Visualize best network
            best_network = extract_single_network(networks, int(best_idx))
            visualize_network(
                network=best_network,
                title=f"Best Network (Gen {generation}, Fitness {best_fitness:.4f})",
                show_weights=show_weights,
                save_path=os.path.join(output_dir, f"network_best_gen{generation:04d}"),
                format=format
            )
            
            # Visualize a few top networks
            if max_networks_per_gen > 1:
                # Get indices of top networks
                top_indices = jnp.argsort(fitnesses)[-max_networks_per_gen:]
                for i, idx in enumerate(top_indices):
                    idx = int(idx)  # Convert JAX array to Python int
                    if idx != int(best_idx):  # Skip best network (already visualized)
                        network = extract_single_network(networks, idx)
                        fitness = float(fitnesses[idx])  # Convert to Python float
                        visualize_network(
                            network=network,
                            title=f"Network {i} (Gen {generation}, Fitness {fitness:.4f})",
                            show_weights=show_weights,
                            save_path=os.path.join(output_dir, f"network_{i}_gen{generation:04d}"),
                            format=format
                        )

    return hook

def plot_decision_boundary(
    network: 'Network',
    dataset_inputs: jnp.ndarray,
    dataset_targets: jnp.ndarray,
    title: str = "Decision Boundary",
    true_boundary_fn: Optional[Callable] = None,
    boundary_params: Optional[Dict[str, Any]] = None,
    save_path: Optional[str] = None,
    show: bool = False,
    resolution: int = 100
) -> Tuple[Figure, Axes]:
    """
    Plot decision boundary for a 2D classification problem.

    Args:
        network: Trained NEAT network
        dataset_inputs: Input data points (N, 2)
        dataset_targets: Target labels (N, 1)
        title: Plot title
        true_boundary_fn: Optional function to plot true boundary (e.g., circle)
        boundary_params: Parameters for true boundary function
        save_path: Optional path to save the plot
        show: Whether to display the plot
        resolution: Grid resolution for decision boundary

    Returns:
        Tuple of (Figure, Axes) objects
    """
    from .network import ActivationState

    # Create figure
    fig, ax = plt.subplots(figsize=(10, 8))

    # Get data bounds with some padding
    x_min, x_max = float(dataset_inputs[:, 0].min()) - 1, float(dataset_inputs[:, 0].max()) + 1
    y_min, y_max = float(dataset_inputs[:, 1].min()) - 1, float(dataset_inputs[:, 1].max()) + 1

    # Create grid for decision boundary
    xx, yy = np.meshgrid(
        np.linspace(x_min, x_max, resolution),
        np.linspace(y_min, y_max, resolution)
    )

    # Prepare grid points for network evaluation
    grid_points = jnp.column_stack([xx.ravel(), yy.ravel()])

    # Create activation state for network
    activation_state = ActivationState(
        node_depths=jnp.full(network.max_nodes, -1, dtype=jnp.int32),
        outdated_depths=True
    )

    # Evaluate network on grid points
    try:
        # Process in batches to avoid memory issues
        batch_size = 1000
        predictions = []

        for i in range(0, len(grid_points), batch_size):
            batch = grid_points[i:i+batch_size]
            outputs, _ = network.forward(batch, activation_state)
            # Apply sigmoid to get probabilities
            probs = jax.nn.sigmoid(outputs[:, 0])
            predictions.append(probs)

        # Combine all predictions
        Z = jnp.concatenate(predictions)
        Z = Z.reshape(xx.shape)

        # Plot decision boundary (probability contours)
        contour = ax.contour(xx, yy, Z, levels=[0.5], colors='black', linewidths=2, linestyles='-')
        ax.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu')

        # Add colorbar
        cbar = plt.colorbar(ax.contourf(xx, yy, Z, levels=50, alpha=0.3, cmap='RdYlBu'), ax=ax)
        cbar.set_label('Network Output Probability', rotation=270, labelpad=20)

    except Exception as e:
        print(f"Warning: Could not plot decision boundary: {e}")

    # Plot dataset points
    inside_mask = dataset_targets[:, 0] > 0
    outside_mask = dataset_targets[:, 0] <= 0

    ax.scatter(dataset_inputs[inside_mask, 0], dataset_inputs[inside_mask, 1],
              c='blue', marker='o', s=50, alpha=0.7, label='Inside (+1)', edgecolors='black', linewidth=0.5)
    ax.scatter(dataset_inputs[outside_mask, 0], dataset_inputs[outside_mask, 1],
              c='red', marker='s', s=50, alpha=0.7, label='Outside (-1)', edgecolors='black', linewidth=0.5)

    # Plot true boundary if provided
    if true_boundary_fn is not None and boundary_params is not None:
        try:
            true_boundary_fn(ax, **boundary_params)
        except Exception as e:
            print(f"Warning: Could not plot true boundary: {e}")

    # Formatting
    ax.set_xlabel('X coordinate', fontsize=12)
    ax.set_ylabel('Y coordinate', fontsize=12)
    ax.set_title(title, fontsize=14, fontweight='bold')
    ax.legend(fontsize=10)
    ax.grid(True, alpha=0.3)
    ax.set_aspect('equal', adjustable='box')

    # Set axis limits
    ax.set_xlim(x_min, x_max)
    ax.set_ylim(y_min, y_max)

    plt.tight_layout()

    # Save if requested
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # Show if requested
    if show:
        plt.show()
    else:
        plt.close()

    return fig, ax

def plot_circle_boundary(ax: Axes, radius: float = 5.0, center: Tuple[float, float] = (0.0, 0.0), **kwargs):
    """Helper function to plot true circle boundary."""
    circle = plt.Circle(center, radius, fill=False, color='green', linewidth=3, linestyle='--', label='True Circle')
    ax.add_patch(circle)

def plot_spiral_boundary(ax: Axes, radius: float = 6.0, turns: float = 1.75, **kwargs):
    """
    Helper function to plot true spiral boundaries for reference.
    
    Args:
        ax: Matplotlib axes to plot on
        radius: Maximum radius of the spirals (default: 6.0)
        turns: Number of turns in the spiral (default: 1.75)
        **kwargs: Additional parameters:
            - n_points: Number of points to use for spiral approximation (default: 200)
    """
    import math
    
    # Generate the theoretical spiral paths
    n_points = kwargs.get('n_points', 200)  # Configurable, defaults to 200
    
    def plot_single_spiral(delta_t: float, color: str, label: str):
        """Plot one theoretical spiral"""
        t_vals = []
        x_vals = []
        y_vals = []
        
        for i in range(n_points):
            r = (i / n_points) * radius
            t = turns * (i / n_points) * 2 * math.pi + delta_t
            x = r * math.sin(t)
            y = r * math.cos(t)
            t_vals.append(t)
            x_vals.append(x)
            y_vals.append(y)
        
        ax.plot(x_vals, y_vals, color=color, linewidth=2, linestyle='--', 
                alpha=0.7, label=f'True {label}')
    
    # Plot both theoretical spirals
    plot_single_spiral(0, 'blue', 'Spiral 1 (+1)')
    plot_single_spiral(math.pi, 'red', 'Spiral 2 (-1)')
    
    # Add a legend note
    ax.text(0, 0, 'Theoretical\nSpiral Paths', ha='center', va='center',
            bbox=dict(boxstyle="round,pad=0.3", facecolor="yellow", alpha=0.5),
            fontsize=8, zorder=10)