"""Tests for the evolution module."""

import os
import pytest
import jax
import jax.numpy as jnp

from neat.evolution import (
    evolve_population, 
    run_evolution
)
from neat.network import NetworkBatch
from neat.species import SpeciesState, initialize_state
from neat.innovation import InnovationTracker
from neat.population import Population, initialize_population, convert_genomes_to_networks
from neat.config.hierarchical_config import NEATConfig, NetworkConfig, SpeciesConfig, MutationConfig, RecombinationConfig, BackpropConfig, FitnessConfig, PopulationConfig

@pytest.fixture
def default_config():
    """Create a default configuration for testing."""
    return NEATConfig.create(
        max_generations=10,
        target_fitness=None,
        seed=42,
        log_progress=False,
        population=PopulationConfig(population_size=20),
        network=NetworkConfig(
            num_inputs=2,
            num_outputs=1,
            max_nodes=10,
            max_connections=20,
            activation_fn="sigmoid",
            hidden_activation="sigmoid",
            output_activation="sigmoid"
        ),
        species=SpeciesConfig(
            max_species=5,
            compatibility_threshold=3.0
        ),
        backprop=BackpropConfig(
            enabled=False,  # Default to False for standard tests
            learning_rate=0.01,
            num_epochs=5,
            batch_size=4
        )
    )

@pytest.fixture
def backprop_config():
    """Create a configuration with backpropagation enabled for testing."""
    return NEATConfig.create(
        max_generations=10,
        target_fitness=None,
        seed=42,
        log_progress=False,
        targets=jnp.array([[0.0], [1.0], [1.0], [0.0]], dtype=jnp.float32),
        population=PopulationConfig(population_size=20),
        network=NetworkConfig(
            num_inputs=2,
            num_outputs=1,
            max_nodes=10,
            max_connections=20,
            activation_fn="sigmoid",
            hidden_activation="sigmoid",
            output_activation="sigmoid"
        ),
        species=SpeciesConfig(
            max_species=5,
            compatibility_threshold=3.0
        ),
        backprop=BackpropConfig(
            enabled=True,
            learning_rate=0.01,
            num_epochs=1,  # Use fewer epochs for testing
            batch_size=4
        )
    )

@pytest.fixture
def mock_task_fn():
    """Create a mock task function that handles both fitness evaluation and backprop patterns."""
    def task_fn(*args):
        if len(args) == 2:
            # Called as loss function during backprop: (predictions, targets)
            predictions, targets = args
            # Simple MSE loss for backprop (return negative because we want to minimize)
            return -jnp.mean((predictions - targets) ** 2)
        elif len(args) == 3:
            # Called as fitness function during evaluation: (network, activation_state, inputs)
            network, activation_state, inputs = args
            # Simple mock that returns higher fitness for networks with more connections
            enabled_count = jnp.sum(network.enabled)
            return jnp.array(enabled_count, dtype=jnp.float32)
        else:
            raise ValueError(f"mock_task_fn called with {len(args)} arguments, expected 2 or 3")

    return task_fn

@pytest.fixture
def initial_state(default_config):
    """Create initial population and species state for testing."""
    key = jax.random.PRNGKey(default_config.seed)
    key, init_key = jax.random.split(key)
    
    tracker = InnovationTracker.create(default_config.network)
    population, tracker = initialize_population(
        key=init_key,
        config=default_config,
        innovation_tracker=tracker
    )
    
    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=default_config
    )
    
    key, species_key = jax.random.split(key)
    species_state = initialize_state(
        connections=population.connections,
        key=species_key,
        config=default_config.species
    )
    
    return {
        "networks": networks,
        "species_state": species_state,
        "tracker": tracker,
        "key": key
    }

@pytest.mark.parametrize("config_name", ["default", "backprop"])
def test_evolve_population(initial_state, default_config, backprop_config, mock_task_fn, config_name):
    """Test that evolve_population produces expected state changes."""
    # Setup
    networks = initial_state["networks"]
    species_state = initial_state["species_state"]
    tracker = initial_state["tracker"]
    key = initial_state["key"]
    inputs = jnp.array([[0.0, 0.0], [0.0, 1.0], [1.0, 0.0], [1.0, 1.0]])
    
    # Select the appropriate config
    config = backprop_config if config_name == "backprop" else default_config
    
    # Run one generation
    new_networks, new_species_state, new_tracker, fitnesses, new_key = evolve_population(
        networks=networks,
        species_state=species_state,
        tracker=tracker,
        key=key,
        fitness_fn=mock_task_fn,
        inputs=inputs,
        config=config
    )
    
    # Verify outputs
    assert isinstance(new_networks, NetworkBatch)
    assert isinstance(new_species_state, SpeciesState)
    assert isinstance(new_tracker, InnovationTracker)
    assert isinstance(fitnesses, jnp.ndarray)
    assert fitnesses.shape == (default_config.population.population_size,)
    assert new_key is not key  # Key should be updated

def test_run_evolution_termination_conditions(default_config, mock_task_fn):
    """Test that run_evolution terminates correctly under different conditions."""
    inputs = jnp.array([[0.0, 0.0], [0.0, 1.0], [1.0, 0.0], [1.0, 1.0]])
    
    # Test max generations termination
    config = default_config.replace(max_generations=5)
    final_networks, best_fitness, best_idx, metrics = run_evolution(
        fitness_fn=mock_task_fn,
        inputs=inputs,
        config=config
    )
    assert metrics['generations'] == config.max_generations
    
    # Test target fitness termination
    # Ensure target_fitness is achievable by mock_task_fn
    # Max possible fitness with mock_task_fn and max_connections=20 is 20.
    # Let's set a target that's reasonably achievable.
    config = default_config.replace(target_fitness=15.0, max_generations=50) # Increased max_generations to allow reaching target
    final_networks, best_fitness, best_idx, metrics = run_evolution(
        fitness_fn=mock_task_fn,
        inputs=inputs,
        config=config
    )
    assert best_fitness >= config.target_fitness
    assert metrics['generations'] <= config.max_generations # Ensure it didn't just hit max_generations

def test_run_evolution_hooks(default_config, mock_task_fn):
    """Test that run_evolution runs successfully (hooks not currently supported)."""
    inputs = jnp.array([[0.0, 0.0], [0.0, 1.0], [1.0, 0.0], [1.0, 1.0]])
    config = default_config.replace(max_generations=3)

    final_networks, best_fitness, best_idx, metrics = run_evolution(
        fitness_fn=mock_task_fn,
        inputs=inputs,
        config=config
    )

    # Just verify that evolution completed successfully
    assert len(metrics['fitness_history']) == config.max_generations
    assert isinstance(best_fitness, jnp.ndarray)

def test_run_evolution_with_backprop(backprop_config, mock_task_fn):
    """Test run_evolution with backpropagation enabled."""
    inputs = jnp.array([[0.0, 0.0], [0.0, 1.0], [1.0, 0.0], [1.0, 1.0]])
    config = backprop_config.replace(max_generations=3) # Keep generations low for test speed

    # To make this test more meaningful for backprop, 
    # we might need a task_fn that actually benefits from weight optimization.
    # The current mock_task_fn only depends on structure.
    # However, we can at least ensure it runs without error.

    final_networks, best_fitness, best_idx, metrics = run_evolution(
        fitness_fn=mock_task_fn, # Using mock_task_fn, but ideally a different one for backprop
        inputs=inputs,
        config=config
    )

    assert isinstance(final_networks, Population)
    assert isinstance(best_fitness, jnp.ndarray)
    assert metrics['generations'] == config.max_generations
    # Add more specific assertions for backprop if possible, e.g., weight changes
    # For now, primarily testing that it runs through.

def test_fitness_stagnation(default_config):
    """Test that evolution handles fitness stagnation correctly."""
    # Create a mock task that stops improving after a few generations
    call_count = 0
    
    def stagnating_task_fn(network, activation_state, inputs):
        nonlocal call_count
        call_count += 1
        # Fitness stops improving after 3 generations
        return jnp.array(10.0 if call_count < 3 else 10.0, dtype=jnp.float32)
    
    # Configure with a small population and few generations
    config = default_config.replace(
        population=PopulationConfig(population_size=5),
        max_generations=10,
        species=SpeciesConfig(
            max_species=5,
            compatibility_threshold=3.0,
            stagnation_threshold=3  # Stagnation after 3 generations without improvement
        )
    )
    
    inputs = jnp.array([[0.0, 0.0], [1.0, 1.0]])
    final_networks, best_fitness, best_idx, metrics = run_evolution(
        fitness_fn=stagnating_task_fn,
        inputs=inputs,
        config=config
    )
    
    # Verify metrics were collected
    assert 'best_fitness' in metrics
    assert 'fitness_history' in metrics
    assert 'species_counts' in metrics
    
    # Verify stagnation was handled (should complete all generations)
    assert metrics['generations'] == config.max_generations
    
    # Best fitness should be the maximum we returned
    assert jnp.max(metrics['fitness_history']) == 10.0

def test_metrics_collection(default_config, mock_task_fn):
    """Test that evolution collects metrics correctly across generations."""
    # Configure with a small number of generations
    config = default_config.replace(
        population=PopulationConfig(population_size=10),
        max_generations=5,
        species=SpeciesConfig(
            max_species=5,
            compatibility_threshold=3.0,
            stagnation_threshold=10  # High to avoid early termination
        )
    )
    
    inputs = jnp.array([[0.0, 0.0], [1.0, 1.0]])
    final_networks, best_fitness, best_idx, metrics = run_evolution(
        config=config,
        fitness_fn=mock_task_fn,
        inputs=inputs
    )
    
    # Check that all expected metrics are present
    expected_metrics = [
        'fitness_history',
        'species_counts',
        'generations',
        'best_fitness',
        'best_idx'
    ]
    
    for metric in expected_metrics:
        assert metric in metrics, f"Missing expected metric: {metric}"
    
    # Check metric lengths match number of generations
    num_generations = metrics['generations']
    assert num_generations == config.max_generations
    
    # Check that fitness_history and species_counts have the right length
    assert len(metrics['fitness_history']) == num_generations
    assert len(metrics['species_counts']) == num_generations
    
    # Check that best fitness is non-decreasing (or at least the best is at the end)
    best_fitnesses = metrics['fitness_history']
    assert jnp.all(jnp.diff(best_fitnesses) >= 0) or best_fitnesses[-1] == max(best_fitnesses), \
        "Best fitness should be non-decreasing or at least the final value should be the best"