"""
Configuration module for Hybrid NEAT.

This module provides configuration classes and default values for the Hybrid NEAT algorithm,
including network architecture, population parameters, species management, recombination,
mutation settings, fitness evaluation, and backpropagation configuration.

The configuration system is hierarchical and modular:
- base.py: Core configuration classes and defaults
- tasks/: Task-specific configuration presets
- presets/: Common configuration presets for different scenarios
"""

# Import core configuration classes
from .base import (
    PopulationConfig,
    NetworkConfig,
    SpeciesConfig,
    RecombinationConfig,
    MutationConfig,
    FitnessConfig,
    BackpropConfig,
    NEATConfig,
    DEFAULT_CONFIG
)

# Import task-specific configurations
from .tasks.xor import (
    XOR_CONFIG
)

from .tasks.circle import (
    CIRCLE_NETWORK_CONFIG,
    CIRCLE_POPULATION_CONFIG,
    CIRCLE_SPECIES_CONFIG,
    CIRCLE_RECOMBINATION_CONFIG,
    CIRCLE_MUTATION_CONFIG,
    CIRCLE_FITNESS_CONFIG,
    CIRCLE_BACKPROP_CONFIG,
    CIRCLE_CONFIG
)

from .tasks.spiral import (
    SPIRAL_NETWORK_CONFIG,
    <PERSON>IRAL_POPULATION_CONFIG,
    SPIRAL_SPECIES_CONFIG,
    SPIRAL_RECOMBINATION_CONFIG,
    SPIRAL_MUTATION_CONFIG,
    SPIRAL_FITNESS_CONFIG,
    SPIRAL_BACKPROP_CONFIG,
    SPIRAL_ENHANCED_BACKPROP_CONFIG,
    SPIRAL_ENHANCED_FITNESS_CONFIG,
    SPIRAL_SIMPLE_CONFIG,
    SPIRAL_FAST_ULTRA_CONFIG,
    SPIRAL_ULTRA_AGGRESSIVE_CONFIG
)

# Export all configuration classes and instances
__all__ = [
    # Core configuration classes
    'PopulationConfig',
    'NetworkConfig',
    'SpeciesConfig',
    'RecombinationConfig',
    'MutationConfig',
    'FitnessConfig',
    'BackpropConfig',
    'NEATConfig',
    'DEFAULT_CONFIG',
    
    # XOR-specific configurations
    'XOR_CONFIG',
    
    # Circle-specific configurations
    'CIRCLE_NETWORK_CONFIG',
    'CIRCLE_POPULATION_CONFIG',
    'CIRCLE_SPECIES_CONFIG',
    'CIRCLE_RECOMBINATION_CONFIG',
    'CIRCLE_MUTATION_CONFIG',
    'CIRCLE_FITNESS_CONFIG',
    'CIRCLE_BACKPROP_CONFIG',
    'CIRCLE_CONFIG',
    
    # Spiral-specific configurations
    'SPIRAL_NETWORK_CONFIG',
    'SPIRAL_POPULATION_CONFIG',
    'SPIRAL_SPECIES_CONFIG',
    'SPIRAL_RECOMBINATION_CONFIG',
    'SPIRAL_MUTATION_CONFIG',
    'SPIRAL_FITNESS_CONFIG',
    'SPIRAL_BACKPROP_CONFIG',
    'SPIRAL_ENHANCED_BACKPROP_CONFIG',
    'SPIRAL_ENHANCED_FITNESS_CONFIG',
    'SPIRAL_SIMPLE_CONFIG',
    'SPIRAL_FAST_ULTRA_CONFIG',
    'SPIRAL_ULTRA_AGGRESSIVE_CONFIG',
]