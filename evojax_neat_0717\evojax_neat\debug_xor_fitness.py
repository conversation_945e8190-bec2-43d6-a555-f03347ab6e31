"""
Debug script to understand why XOR fitness is stuck at 3.0
"""

import jax
import jax.numpy as jnp
import sys
import os

# Add the current directory to the path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from evojax.algo.neat_core.config import XOR_CONFIG
from evojax.algo.neat_core.network import ActivationState, extract_single_network
from evojax.algo.neat_core.loss_fns import mean_squared_error
from evojax.algo.neat_core import initialize_population, InnovationTracker, convert_genomes_to_networks
from evojax.algo.neat_core.tasks.xor_task import evaluate_xor_fitness

# XOR dataset
XOR_INPUTS = jnp.array([[-1.0, -1.0], [-1.0, 1.0], [1.0, -1.0], [1.0, 1.0]])
XOR_TARGETS = jnp.array([[-1.0], [1.0], [1.0], [-1.0]])

print("🔍 Debugging XOR Fitness Evaluation")
print("=" * 50)

# Initialize a small population
key = jax.random.PRNGKey(42)
tracker = InnovationTracker.create(XOR_CONFIG.network)

key, init_key = jax.random.split(key)
population, tracker = initialize_population(
    key=init_key,
    innovation_tracker=tracker,
    config=XOR_CONFIG
)

# Convert to networks
networks, _ = convert_genomes_to_networks(
    connections=population.connections,
    enabled=population.enabled,
    config=XOR_CONFIG
)

print(f"✓ Created population with {networks.connections.shape[0]} networks")

# Test the first network
single_network = extract_single_network(networks, 0)
activation_state = ActivationState(
    node_depths=jnp.full(single_network.max_nodes, -1, dtype=jnp.int32),
    outdated_depths=True
)

print(f"\n🧪 Testing first network:")
print(f"Network structure:")
print(f"  - Inputs: {single_network.num_inputs}")
print(f"  - Outputs: {single_network.num_outputs}")
print(f"  - Max nodes: {single_network.max_nodes}")
print(f"  - Node types: {single_network.node_types}")
print(f"  - Connections shape: {single_network.connections.shape}")
print(f"  - Enabled connections: {jnp.sum(single_network.enabled)}")

# Test forward pass
print(f"\n🔬 Testing forward pass:")
for i, input_pair in enumerate(XOR_INPUTS):
    try:
        output, _ = single_network.forward(input_pair.reshape(1, -1), activation_state)
        expected = XOR_TARGETS[i][0]
        actual = output[0, 0]
        error = abs(expected - actual)
        
        print(f"  XOR({input_pair[0]:+.0f},{input_pair[1]:+.0f}): expected={expected:+.3f}, actual={actual:+.6f}, error={error:.6f}")
    except Exception as e:
        print(f"  XOR({input_pair[0]:+.0f},{input_pair[1]:+.0f}): ERROR - {e}")

# Test batch forward pass
print(f"\n🧮 Testing batch forward pass:")
try:
    outputs, _ = single_network.forward(XOR_INPUTS, activation_state)
    print(f"  Batch outputs shape: {outputs.shape}")
    print(f"  Batch outputs: {outputs.flatten()}")
    print(f"  Expected targets: {XOR_TARGETS.flatten()}")
    
    mse = mean_squared_error(outputs, XOR_TARGETS)
    fitness = 4.0 - mse
    print(f"  MSE: {mse:.6f}")
    print(f"  Fitness: {fitness:.6f}")
    
except Exception as e:
    print(f"  Batch forward pass ERROR: {e}")

# Test fitness function directly
print(f"\n🎯 Testing fitness function:")
try:
    fitness = evaluate_xor_fitness(single_network, activation_state, XOR_INPUTS)
    print(f"  Direct fitness call: {fitness:.6f}")
except Exception as e:
    print(f"  Fitness function ERROR: {e}")

# Test multiple networks
print(f"\n📊 Testing multiple networks:")
fitness_scores = []
for i in range(min(5, networks.connections.shape[0])):
    try:
        net = extract_single_network(networks, i)
        state = ActivationState(
            node_depths=jnp.full(net.max_nodes, -1, dtype=jnp.int32),
            outdated_depths=True
        )
        fitness = evaluate_xor_fitness(net, state, XOR_INPUTS)
        fitness_scores.append(fitness)
        print(f"  Network {i}: fitness = {fitness:.6f}")
    except Exception as e:
        print(f"  Network {i}: ERROR - {e}")

if fitness_scores:
    print(f"\n📈 Fitness statistics:")
    print(f"  Mean: {jnp.mean(jnp.array(fitness_scores)):.6f}")
    print(f"  Std:  {jnp.std(jnp.array(fitness_scores)):.6f}")
    print(f"  Min:  {jnp.min(jnp.array(fitness_scores)):.6f}")
    print(f"  Max:  {jnp.max(jnp.array(fitness_scores)):.6f}")

print(f"\n🔍 Analysis:")
if all(abs(f - 3.0) < 0.001 for f in fitness_scores):
    print("❌ All networks have fitness ~3.0 (MSE ~1.0)")
    print("   This suggests networks are producing random/zero outputs")
    print("   Possible issues:")
    print("   - Network initialization problem")
    print("   - Forward pass not working correctly")
    print("   - Activation functions not working")
    print("   - Connection weights are all zero")
else:
    print("✅ Networks show fitness variation - evolution should work")
