import chex
import jax
import jax.numpy as jnp
from typing import Optional

# --- Basic Utility Functions ---

def find_first(
    mask: jnp.ndarray,
    default: int = -1
) -> jnp.ndarray:
    """Find the index of the first True value in a boolean mask.

    Args:
        mask: Boolean array to search through (must be 1D)
        default: Value to return if no True values found

    Returns:
        Index of first True element as jnp.int32, or default value if none found
    """
    # Ensure mask is 1D
    chex.assert_rank(mask, 1)

    # Find the index of the first True value
    idx = jnp.argmax(mask)

    # If no True values found, return default value, otherwise return the index
    return jax.lax.select(
        jnp.any(mask),
        idx.astype(jnp.int32),
        jnp.array(default, dtype=jnp.int32)
    )

def check_probability(
    key: chex.PRNGKey,
    rate: float,
    high_rate: float = 0.9,
    use_high_rate: bool = False
) -> jnp.ndarray:
    """Check if a mutation should be applied based on probability.

    Args:
        key: Random key for uniform distribution sampling
        rate: Base mutation rate
        high_rate: Rate to use if use_high_rate is True
        use_high_rate: Whether to use the high mutation rate

    Returns:
        Boolean value indicating if the probability check passed
    """
    actual_rate = jax.lax.select(use_high_rate, high_rate, rate)
    return jax.random.uniform(key) < actual_rate

# --- Search and Selection Functions ---

def sample_from_mask(
    key: chex.PRNGKey,
    mask: jnp.ndarray,
    indices: Optional[jnp.ndarray] = None
) -> jnp.ndarray:
    """Sample an index from a boolean mask with uniform probability.

    Selects a random index from positions where mask is True, with uniform probability.
    If indices is provided, the mask is applied to those indices, and the result
    is one of the provided indices. If the mask is empty (all False), returns -1.

    Args:
        key: JAX PRNG key for random sampling
        mask: Boolean mask indicating valid indices to sample from
        indices: Optional array of indices to map the mask to

    Returns:
        Sampled index as a JAX array (or -1 if mask is empty)
    """
    # Generate uniform random values for all positions, then mask invalid positions with -infinity.
    # Taking argmax gives a uniform random sample from the valid positions (where mask is True).
    mask_size = mask.shape[0]
    rand_vals = jax.random.uniform(key, (mask_size,))

    # Set random values to -infinity where mask is False
    masked_vals = jnp.where(mask, rand_vals, -jnp.inf)

    # Get the position with the highest random value (uniform sample from valid positions)
    mask_idx = jnp.argmax(masked_vals)

    # Map to provided indices if available
    if indices is not None:
        mask_idx = indices[mask_idx]

    # Return the sampled index, or -1 if mask is empty
    return jax.lax.cond(
        jnp.any(mask),
        lambda _: mask_idx,
        lambda _: jnp.array(-1, dtype=jnp.int32),
        operand=None
    )

