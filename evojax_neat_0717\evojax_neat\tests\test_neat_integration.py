"""
Integration tests for NEAT algorithm with EvoJAX interface.

This module tests the NEAT algorithm integration to ensure it works correctly
with the EvoJAX framework and follows the expected interface patterns.
"""

import pytest
import jax
import jax.numpy as jnp
import numpy as np

from evojax.algo import NEAT, Strategies
from evojax.algo.neat_core import DEFAULT_NEAT_CONFIG, create_neat_config, XOR_CONFIG


class TestNEATIntegration:
    """Test suite for NEAT-EvoJAX integration."""

    def test_neat_import(self):
        """Test that NEAT can be imported correctly."""
        # Test direct import
        assert NEAT is not None
        
        # Test import through Strategies
        assert "NEAT" in Strategies
        neat_class = Strategies["NEAT"]
        assert neat_class is NEAT

    def test_neat_initialization(self):
        """Test NEAT algorithm initialization."""
        # Test with default config
        neat = NEAT(seed=42)
        assert neat.pop_size == 150  # Default population size
        assert neat.param_size > 0
        assert neat._generation == 0
        
        # Test with custom config
        custom_config = create_neat_config(
            num_inputs=3,
            num_outputs=2,
            population_size=50
        )
        neat_custom = NEAT(config=custom_config, seed=42)
        assert neat_custom.pop_size == 50
        assert neat_custom.config.network.num_inputs == 3
        assert neat_custom.config.network.num_outputs == 2

    def test_neat_ask_method(self):
        """Test the ask method returns correct parameter format."""
        neat = NEAT(seed=42)
        
        # Test ask returns correct shape
        params = neat.ask()
        assert isinstance(params, jnp.ndarray)
        assert params.shape == (neat.pop_size, neat.param_size)
        assert params.dtype == jnp.float32
        
        # Test multiple calls return same parameters (until tell is called)
        params2 = neat.ask()
        assert jnp.allclose(params, params2)

    def test_neat_tell_method(self):
        """Test the tell method processes fitness correctly."""
        neat = NEAT(seed=42)
        params = neat.ask()
        
        # Create dummy fitness values
        fitness = jnp.ones(neat.pop_size) * 0.5
        fitness = fitness.at[0].set(1.0)  # Make first individual best
        
        # Test tell method
        initial_generation = neat._generation
        neat.tell(fitness)
        
        # Check that generation incremented
        assert neat._generation == initial_generation + 1
        
        # Check that best params were updated
        best_params = neat.best_params
        assert isinstance(best_params, jnp.ndarray)
        assert best_params.shape == (neat.param_size,)

    def test_neat_ask_tell_cycle(self):
        """Test multiple ask/tell cycles work correctly."""
        neat = NEAT(seed=42)
        
        for generation in range(3):
            # Ask for parameters
            params = neat.ask()
            assert params.shape == (neat.pop_size, neat.param_size)
            
            # Create fitness (simple quadratic function)
            fitness = -jnp.sum(params**2, axis=1)
            
            # Tell fitness
            neat.tell(fitness)
            
            # Check generation incremented
            assert neat._generation == generation + 1

    def test_neat_state_management(self):
        """Test save and load state functionality."""
        neat = NEAT(seed=42)
        
        # Run a few generations
        for _ in range(2):
            params = neat.ask()
            fitness = jnp.ones(neat.pop_size)
            neat.tell(fitness)
        
        # Save state
        saved_state = neat.save_state()
        assert isinstance(saved_state, dict)
        assert 'generation' in saved_state
        assert 'population' in saved_state
        assert 'best_params' in saved_state
        
        # Create new NEAT instance and load state
        neat2 = NEAT(seed=123)  # Different seed
        neat2.load_state(saved_state)
        
        # Check state was loaded correctly
        assert neat2._generation == neat._generation
        assert jnp.allclose(neat2.best_params, neat.best_params)

    def test_neat_network_access(self):
        """Test direct access to NEAT networks."""
        neat = NEAT(seed=42)
        
        # Get networks
        networks = neat.get_networks()
        assert hasattr(networks, 'connections')
        assert hasattr(networks, 'enabled')
        
        # Check network batch shape
        assert networks.connections.shape[0] == neat.pop_size
        assert networks.enabled.shape[0] == neat.pop_size

    def test_neat_population_info(self):
        """Test population information retrieval."""
        neat = NEAT(seed=42)
        
        info = neat.get_population_info()
        assert isinstance(info, dict)
        assert 'generation' in info
        assert 'population_size' in info
        assert 'num_species' in info
        assert 'innovation_count' in info
        assert 'config' in info
        
        # Check values are reasonable
        assert info['generation'] == 0
        assert info['population_size'] == neat.pop_size
        assert info['innovation_count'] > 0

    def test_neat_with_xor_config(self):
        """Test NEAT with XOR configuration."""
        neat = NEAT(config=XOR_CONFIG, seed=42)
        
        assert neat.config.network.num_inputs == 2
        assert neat.config.network.num_outputs == 1
        assert neat.pop_size == XOR_CONFIG.population.population_size
        
        # Test basic functionality
        params = neat.ask()
        fitness = jnp.ones(neat.pop_size)
        neat.tell(fitness)
        
        assert neat._generation == 1

    def test_neat_parameter_consistency(self):
        """Test that parameters remain consistent across operations."""
        neat = NEAT(seed=42)
        
        # Get initial parameters
        params1 = neat.ask()
        
        # Get parameters again (should be same)
        params2 = neat.ask()
        assert jnp.allclose(params1, params2)
        
        # After tell, parameters should change
        fitness = jnp.ones(neat.pop_size)
        neat.tell(fitness)
        
        params3 = neat.ask()
        # Note: Currently parameters don't change because evolution is placeholder
        # This test documents current behavior and should be updated when evolution is implemented

    def test_neat_fitness_tracking(self):
        """Test that fitness values are tracked correctly."""
        neat = NEAT(seed=42)
        
        # Test with varying fitness
        params = neat.ask()
        fitness = jnp.linspace(0.1, 1.0, neat.pop_size)
        
        neat.tell(fitness)
        
        # Best fitness should be the maximum
        best_params = neat.best_params
        assert best_params is not None
        assert best_params.shape == (neat.param_size,)


def test_neat_integration_basic():
    """Basic integration test that can be run standalone."""
    print("Running basic NEAT integration test...")
    
    # Create NEAT instance
    neat = NEAT(seed=42)
    print(f"Created NEAT with population size: {neat.pop_size}")
    
    # Run a few generations
    for gen in range(3):
        params = neat.ask()
        fitness = -jnp.sum(params**2, axis=1)  # Simple quadratic fitness
        neat.tell(fitness)
        
        best_fitness = jnp.max(fitness)
        mean_fitness = jnp.mean(fitness)
        print(f"Generation {gen + 1}: Best={best_fitness:.4f}, Mean={mean_fitness:.4f}")
    
    print("Basic integration test completed successfully!")


if __name__ == "__main__":
    test_neat_integration_basic()
