# Import available task modules
try:
    from .xor_task import main as run_xor_task
    _xor_available = True
except ImportError:
    _xor_available = False

try:
    from .circle_task import main as run_circle_task
    _circle_available = True
except ImportError:
    _circle_available = False

try:
    from .spiral_task import main as run_spiral_task
    _spiral_available = True
except ImportError:
    _spiral_available = False

__all__ = []
if _xor_available:
    __all__.append("run_xor_task")
if _circle_available:
    __all__.append("run_circle_task")
if _spiral_available:
    __all__.append("run_spiral_task")