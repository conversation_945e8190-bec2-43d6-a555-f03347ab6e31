import jax
import jax.numpy as jnp
import pytest
from jax import random
from flax import struct
from dataclasses import dataclass

# Import NEAT components
from ..population import (
    Population,
    initialize_population,
    convert_genomes_to_networks,
)
from ..network import NetworkBatch
from ..innovation import InnovationTracker
from ..config.base import NEATConfig, NetworkConfig, PopulationConfig, MutationConfig
from ..constants import (
    NODE_BIAS,
    NODE_INPUT,
    NODE_OUTPUT,
    NODE_HIDDEN,
    NODE_UNUSED,
    EMPTY_SLOT,
)

# Default test values
DEFAULT_NUM_INPUTS = 2
DEFAULT_NUM_OUTPUTS = 1
DEFAULT_MAX_NODES = 10
DEFAULT_MAX_CONNECTIONS = 20

# Activation function indices (constants)
# Import activation constants from neat.constants
from ..constants import ACTIVATION_IDENTITY, ACTIVATION_SIGMOID, ACT_FN_MAP

@struct.dataclass
class NetworkConfigTest(NetworkConfig):
    """Test configuration for network parameters."""
    max_connections: int = 100
    num_inputs: int = 3
    num_outputs: int = 2
    max_nodes: int = 100

def create_test_config(
    num_inputs: int = DEFAULT_NUM_INPUTS,
    num_outputs: int = DEFAULT_NUM_OUTPUTS,
    max_nodes: int = DEFAULT_MAX_NODES,
    max_connections: int = DEFAULT_MAX_CONNECTIONS,
    activation_fn: str = "sigmoid",
    output_activation: str = "sigmoid",
    hidden_activation: str = "tanh",
    population_size: int = 10,
    weight_init_std: float = 1.0,
    weight_init_mean: float = 0.0
) -> NEATConfig:
    """Helper function to create a test configuration."""
    return NEATConfig.create(
        population=PopulationConfig(
            population_size=population_size,
            weight_init_std=weight_init_std,
            weight_init_mean=weight_init_mean
        ),
        network=NetworkConfig(
            num_inputs=num_inputs,
            num_outputs=num_outputs,
            max_nodes=max_nodes,
            max_connections=max_connections,
            activation_fn=activation_fn,
            output_activation=output_activation,
            hidden_activation=hidden_activation
        )
    )

# =============================================================================
# Tests for initialize_population
# =============================================================================

@pytest.fixture
def rng_key():
    return random.PRNGKey(42)

@pytest.fixture
def test_config() -> NetworkConfigTest:
    """Create a test configuration with all necessary components."""
    return NetworkConfigTest()

@pytest.fixture
def tracker(test_config) -> InnovationTracker:
    """Create an innovation tracker for testing."""
    return InnovationTracker.create(test_config)

def test_population_initialize_defaults(rng_key, test_config):
    """Test Population initialization with default parameters."""
    tracker = InnovationTracker.create(test_config)
    config = NEATConfig.create(
        population=PopulationConfig(population_size=10, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=test_config.num_inputs,
            num_outputs=test_config.num_outputs,
            max_nodes=test_config.max_nodes,
            max_connections=test_config.max_connections,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )

    population, updated_tracker = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )

    # Get values from config instead of using constants directly
    num_inputs = config.network.num_inputs
    num_outputs = config.network.num_outputs
    max_connections = config.network.max_connections

    # Check shape and structure
    assert population.connections.shape == (config.population.population_size, max_connections, 4)
    assert population.enabled.shape == (config.population.population_size, max_connections)
    assert population.num_connections.shape == (config.population.population_size,)

    # Check initial connections (bias->output + input->output per genome)
    expected_connections = num_outputs + (num_inputs * num_outputs)  # bias->output + input->output
    assert jnp.all(population.num_connections == expected_connections)

    # Check that connections are properly enabled
    assert jnp.all(population.enabled[:, :expected_connections])
    assert jnp.all(~population.enabled[:, expected_connections:])

    # Check that unused connection slots have EMPTY_SLOT (-1) for sender and receiver
    assert jnp.all(population.connections[:, expected_connections:, 0] == EMPTY_SLOT)
    assert jnp.all(population.connections[:, expected_connections:, 1] == EMPTY_SLOT)
    
    # Check innovation tracking was updated
    assert jnp.all(updated_tracker.next_innovation_id > 0)  # Should have innovations for all connections
    
    # Check node ordering and connections
    for i in range(population.connections.shape[0]):
        conns = population.connections[i]
        enabled = population.enabled[i]
        num_conn = population.num_connections[i]
        
        # First num_outputs connections should be bias->output
        bias_conns = conns[:num_outputs]
        assert jnp.all(bias_conns[:, 0] == 0)  # All senders are bias node (0)
        assert jnp.all(bias_conns[:, 1] >= num_inputs + 1)  # All receivers are output nodes
        
        # Remaining connections should be input->output
        input_conns = conns[num_outputs:num_conn]
        assert jnp.all(input_conns[:, 0] >= 1)  # All senders are input nodes (1 to num_inputs)
        assert jnp.all(input_conns[:, 0] <= num_inputs)  # Input nodes are 1 to num_inputs
        assert jnp.all(input_conns[:, 1] >= num_inputs + 1)  # All receivers are output nodes

def test_population_initialize_custom(rng_key, test_config):
    """Test Population initialization with custom parameters."""
    tracker = InnovationTracker.create(test_config)
    # Custom parameters
    num_inputs = 3
    num_outputs = 2
    max_connections = 100
    max_nodes = 50
    population_size = 5

    config = NEATConfig.create(
        population=PopulationConfig(population_size=population_size, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=num_inputs,
            num_outputs=num_outputs,
            max_nodes=max_nodes,
            max_connections=max_connections,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )

    population, updated_tracker = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )

    # Check shape and structure with custom parameters
    assert population.connections.shape == (population_size, max_connections, 4)
    assert population.enabled.shape == (population_size, max_connections)
    assert population.num_connections.shape == (population_size,)

    # Check initial connections (bias->output + input->output per genome)
    expected_connections = num_outputs + (num_inputs * num_outputs)  # bias->output + input->output
    assert jnp.all(population.num_connections == expected_connections)

    # Check that connections are properly enabled
    assert jnp.all(population.enabled[:, :expected_connections])
    assert jnp.all(~population.enabled[:, expected_connections:])

    # Check that unused connection slots have EMPTY_SLOT (-1) for sender and receiver
    assert jnp.all(population.connections[:, expected_connections:, 0] == EMPTY_SLOT)
    assert jnp.all(population.connections[:, expected_connections:, 1] == EMPTY_SLOT)

    # Check innovation tracking was updated
    assert jnp.all(updated_tracker.next_innovation_id > 0)  # Should have innovations for all connections

# =============================================================================
# Tests for get_genome_data
# =============================================================================

def test_get_genome_data(rng_key, test_config):
    """Test the get_genome_data instance method."""
    tracker = InnovationTracker.create(test_config)
    # Create a small population using the test config helper
    max_connections = 10
    num_inputs = 2
    num_outputs = 1

    config = NEATConfig.create(
        population=PopulationConfig(population_size=3, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=num_inputs,
            num_outputs=num_outputs,
            max_connections=max_connections,
            max_nodes=20,  # Arbitrary value for testing
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )

    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )

    # Test getting data for first genome
    connections, enabled, num_conn = population.get_genome_data(0)

    # Check shapes
    assert connections.shape == (max_connections, 4)  # max_connections x 4
    assert enabled.shape == (max_connections,)
    assert isinstance(num_conn, jnp.ndarray)

    # Check that we get the correct number of connections
    expected_connections = num_outputs + (num_inputs * num_outputs)  # bias->output + input->output
    assert num_conn == expected_connections, \
        f"Expected {expected_connections} connections (bias->output + input->output), got {num_conn}"

    # Check that the first num_outputs connections are bias->output
    assert jnp.all(connections[:num_outputs, 0] == 0)  # All senders are bias node (0)
    assert jnp.all(connections[:num_outputs, 1] >= num_inputs + 1)  # All receivers are output nodes

    # Check that the remaining connections are input->output
    input_conns = connections[num_outputs:num_conn]
    assert jnp.all(input_conns[:, 0] >= 1)  # All senders are input nodes (1 to num_inputs)
    assert jnp.all(input_conns[:, 0] <= num_inputs)  # Input nodes are 1 to num_inputs
    assert jnp.all(input_conns[:, 1] >= num_inputs + 1)  # All receivers are output nodes

# =============================================================================
# Tests for update_genome
# =============================================================================

def test_update_genome(rng_key, test_config):
    """Test the update_genome instance method."""
    tracker = InnovationTracker.create(test_config)
    # Create a small population with custom config
    population_size = 5
    max_connections = 10
    max_nodes = 20  # Arbitrary value for testing
    
    config = NEATConfig.create(
        population=PopulationConfig(population_size=population_size, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=2,
            num_outputs=1,
            max_connections=max_connections,
            max_nodes=max_nodes,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )
    
    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )
    
    # Create modified genome data
    genome_idx = jnp.array(3)
    original_connections = population.connections[genome_idx]
    modified_connections = original_connections.at[0, 2].set(0.5)  # Change first connection weight

    original_enabled = population.enabled[genome_idx]
    modified_enabled = original_enabled.at[0].set(False)  # Disable first connection

    original_num_connections = population.num_connections[genome_idx]
    modified_num_connections = original_num_connections + jnp.array(1)  # Increment count

    # Update the population with modified genome
    updated_population = population.update_genome(
        genome_idx=genome_idx,
        connections=modified_connections,
        enabled=modified_enabled,
        num_connections=modified_num_connections
    )

    # Verify the update was applied correctly
    assert jnp.array_equal(updated_population.connections[genome_idx], modified_connections), \
        "Connections were not updated correctly"
    assert jnp.array_equal(updated_population.enabled[genome_idx], modified_enabled), \
        "Enabled flags were not updated correctly"
    assert jnp.array_equal(updated_population.num_connections[genome_idx], modified_num_connections), \
        "Connection count was not updated correctly"

    # Verify other genomes were not affected
    for i in range(population_size):
        if i != genome_idx:
            assert jnp.array_equal(updated_population.connections[i], population.connections[i]), \
                f"Connections for genome {i} were unexpectedly modified"
            assert jnp.array_equal(updated_population.enabled[i], population.enabled[i]), \
                f"Enabled flags for genome {i} were unexpectedly modified"
            assert jnp.array_equal(updated_population.num_connections[i], population.num_connections[i]), \
                f"Connection count for genome {i} was unexpectedly modified"

# =============================================================================
# Tests for replace method
# =============================================================================

def test_replace(rng_key, test_config):
    """Test the built-in replace method from Flax."""
    tracker = InnovationTracker.create(test_config)
    # Create a small population with custom config
    population_size = 3
    max_connections = 10
    max_nodes = 20  # Arbitrary value for testing
    
    config = NEATConfig.create(
        population=PopulationConfig(population_size=population_size, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=2,
            num_outputs=1,
            max_connections=max_connections,
            max_nodes=max_nodes,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )
    
    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )
    
    # Create a new population with modified attributes
    new_connections = jnp.ones_like(population.connections) * 0.5
    new_enabled = jnp.ones_like(population.enabled, dtype=bool)
    new_num_connections = jnp.ones_like(population.num_connections) * 5
    
    # Use replace to create a new population with updated fields
    updated_population = population.replace(
        connections=new_connections,
        enabled=new_enabled,
        num_connections=new_num_connections
    )
    
    # Check that the new population has the updated values
    assert jnp.array_equal(updated_population.connections, new_connections), \
        "Connections were not updated correctly in replace"
    assert jnp.array_equal(updated_population.enabled, new_enabled), \
        "Enabled flags were not updated correctly in replace"
    assert jnp.array_equal(updated_population.num_connections, new_num_connections), \
        "Connection counts were not updated correctly in replace"

# =============================================================================
# Tests for edge cases
# =============================================================================

def test_population_initialization_with_bias(rng_key, test_config):
    """Test that all networks in a new population have a bias node and bias->output connections."""
    tracker = InnovationTracker.create(test_config)
    # Create a small population with known parameters
    num_inputs = 2
    num_outputs = 1
    max_connections = 10
    population_size = 5

    # Create config with test parameters
    config = NEATConfig.create(
        population=PopulationConfig(population_size=population_size, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=num_inputs,
            num_outputs=num_outputs,
            max_connections=max_connections,
            max_nodes=20,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )

    # Initialize population
    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )

    # Check each genome in the population
    for i in range(population.connections.shape[0]):
        conns = population.connections[i]
        enabled = population.enabled[i]
        num_conn = population.num_connections[i]

        # Should have at least one bias connection (bias -> output)
        bias_conn_mask = (conns[:num_conn, 0] == 0) & enabled[:num_conn]
        assert jnp.any(bias_conn_mask), f"No bias connection found in genome {i}"

        # Check that bias only connects to output nodes
        # Output nodes start after bias + input nodes (1 + num_inputs)
        output_node_start = 1 + num_inputs
        bias_conns = conns[:num_conn][bias_conn_mask]
        assert jnp.all(bias_conns[:, 1] >= output_node_start), \
            f"Bias node connects to non-output node in genome {i}"

def test_innovation_tracking_consistency(rng_key, test_config):
    """Test that innovation IDs are consistent across identical topologies in initial population."""
    tracker = InnovationTracker.create(test_config)
    pop_size = 10
    num_inputs = 2
    num_outputs = 1
    max_connections = 20
    max_nodes = 30

    # Create config with test parameters using the helper
    config = NEATConfig.create(
        population=PopulationConfig(population_size=pop_size, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=num_inputs,
            num_outputs=num_outputs,
            max_connections=max_connections,
            max_nodes=max_nodes,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )

    # Initialize population and get updated tracker
    population, updated_tracker = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )

    # In initial population, all genomes should have identical topology
    # Expected connections: bias->output, input1->output, input2->output
    expected_connections = num_outputs + (num_inputs * num_outputs)  # 1 + (2 * 1) = 3
    
    # Get innovation IDs from first genome as reference
    first_genome_innovations = population.connections[0, :expected_connections, 3].tolist()
    
    # Check that all genomes have identical innovation IDs (same topology)
    for i in range(pop_size):
        genome_innovations = population.connections[i, :expected_connections, 3].tolist()
        assert genome_innovations == first_genome_innovations, \
            f"Genome {i} has different innovation IDs than genome 0: {genome_innovations} vs {first_genome_innovations}"
    
    # Check that the innovation IDs are sequential (0, 1, 2, ...)
    expected_ids = list(range(expected_connections))
    assert first_genome_innovations == expected_ids, \
        f"Expected sequential innovation IDs {expected_ids}, got {first_genome_innovations}"
    
    # Verify the tracker has been updated correctly
    assert updated_tracker.next_innovation_id == expected_connections, \
        f"Expected next innovation ID to be {expected_connections}, got {updated_tracker.next_innovation_id}"

# =============================================================================
# Tests for dtype consistency
# =============================================================================

def test_dtype_integrity(test_config):
    """Verify correct dtypes for all population components."""
    rng_key = jax.random.PRNGKey(0)
    tracker = InnovationTracker.create(test_config)
    
    # Create a test config using the helper
    config = NEATConfig.create(
        population=PopulationConfig(population_size=5, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=2,
            num_outputs=1,
            max_nodes=30,
            max_connections=20,
            activation_fn="sigmoid",
            output_activation="sigmoid",
            hidden_activation="tanh"
        )
    )

    # Initialize population
    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )

    # Check dtypes
    assert population.connections.dtype == jnp.float32, \
        f"Expected connections.dtype to be float32, got {population.connections.dtype}"
    assert population.enabled.dtype == jnp.bool_, \
        f"Expected enabled.dtype to be bool, got {population.enabled.dtype}"
    assert population.num_connections.dtype == jnp.int32, \
        f"Expected num_connections.dtype to be int32, got {population.num_connections.dtype}"

# Removed duplicate create_test_config function - using the one defined above

# =============================================================================
# Tests for create_networks
# =============================================================================

@pytest.fixture
def sample_population(test_config) -> tuple[Population, InnovationTracker]:
    """Create a deterministic test population."""
    rng_key = jax.random.PRNGKey(42)
    tracker = InnovationTracker.create(test_config)
    
    # Create a small population with known properties
    config = NEATConfig.create(
        population=PopulationConfig(population_size=5, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=test_config.num_inputs,
            num_outputs=test_config.num_outputs,
            max_nodes=test_config.max_nodes,
            max_connections=test_config.max_connections,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )
    
    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )
    
    return population, tracker

def test_network_conversion_basic_properties(sample_population):
    """Test basic properties of network conversion."""
    population, _ = sample_population
    pop_size = len(population.connections)

    # Convert all genomes to networks using test config
    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=create_test_config(activation_fn="tanh", hidden_activation="tanh", output_activation="sigmoid")
    )

    # Basic shape checks
    assert isinstance(networks, NetworkBatch)
    assert networks.node_types.shape == (pop_size, DEFAULT_MAX_NODES)
    assert networks.activation_fns.shape == (pop_size, DEFAULT_MAX_NODES)

    # Check node types for each network in batch
    for i in range(pop_size):
        # First node should be bias
        assert networks.node_types[i, 0] == NODE_BIAS
        # Next nodes should be inputs
        assert jnp.all(networks.node_types[i, 1:1+DEFAULT_NUM_INPUTS] == NODE_INPUT)
        # Output nodes come right after input nodes (NEAT indexing)
        output_start = DEFAULT_NUM_INPUTS + 1
        output_end = output_start + DEFAULT_NUM_OUTPUTS
        assert jnp.all(networks.node_types[i, output_start:output_end] == NODE_OUTPUT)

        # Check activation functions
        assert networks.activation_fns[i, 0] == ACTIVATION_IDENTITY  # Bias node
        assert jnp.all(networks.activation_fns[i, output_start:output_end] == ACTIVATION_SIGMOID)  # Output nodes

def test_network_conversion_deterministic(sample_population):
    """Test that conversion is deterministic with same inputs."""
    population, _ = sample_population
    
    def convert():
        return convert_genomes_to_networks(
            connections=population.connections,
            enabled=population.enabled,
            config=create_test_config(activation_fn="tanh", hidden_activation="tanh", output_activation="sigmoid")
        )
    
    # First conversion
    networks1, indices1 = convert()
    # Second conversion should be identical
    networks2, indices2 = convert()
    
    # Check all fields are equal
    for field in ['node_types', 'activation_fns']:
        assert jnp.array_equal(getattr(networks1, field), getattr(networks2, field))
    assert jnp.array_equal(indices1, indices2)

def test_network_conversion_edge_cases():
    """Test network conversion with edge cases."""
    # Test empty connections
    empty_connections = jnp.zeros((1, 0, 4), dtype=jnp.int32)  # Add batch dimension
    empty_enabled = jnp.zeros((1, 0), dtype=bool)  # Add batch dimension

    with pytest.raises(ValueError):
        test_config = NEATConfig.create(
            population=PopulationConfig(population_size=1, weight_init_std=1.0, weight_init_mean=0.0),
            network=NetworkConfig(
                num_inputs=2,
                num_outputs=1,
                max_nodes=10,
                activation_fn="sigmoid"
            )
        )
        convert_genomes_to_networks(
            connections=empty_connections,
            enabled=empty_enabled,
            config=test_config
        )

    # Test with minimum valid connections
    min_connections = jnp.array([[[0, 3, 1.0, 0]]], dtype=jnp.int32)  # Single bias->output connection
    min_enabled = jnp.array([[True]], dtype=bool)

    networks, _ = convert_genomes_to_networks(
        connections=min_connections,
        enabled=min_enabled,
        config=create_test_config(activation_fn="tanh", hidden_activation="tanh", output_activation="sigmoid")
    )

    assert networks.node_types.shape == (1, DEFAULT_MAX_NODES)
    assert networks.activation_fns.shape == (1, DEFAULT_MAX_NODES)

def test_network_conversion_disabled_connections(sample_population):
    """Test that disabled connections are handled correctly."""
    population, _ = sample_population
    pop_size = len(population.connections)
    
    # Disable some connections
    enabled = population.enabled.at[0, ::2].set(False)
    
    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=enabled,
        config=create_test_config(activation_fn="tanh", hidden_activation="tanh", output_activation="sigmoid")
    )
    
    # Check that disabled connections don't affect network structure
    assert networks.node_types.shape == (pop_size, DEFAULT_MAX_NODES)

def test_network_conversion_large_population(test_config):
    """Test conversion with a larger population size."""
    pop_size = 100
    rng_key = jax.random.PRNGKey(42)
    tracker = InnovationTracker.create(test_config)
    config = NEATConfig.create(
        population=PopulationConfig(population_size=pop_size, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=test_config.num_inputs,
            num_outputs=test_config.num_outputs,
            max_nodes=test_config.max_nodes,
            max_connections=test_config.max_connections,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )
    
    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )
    
    # Time the conversion
    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=config
    )
    
    # Verify all networks were processed
    assert len(networks.node_types) == pop_size
    assert jnp.all(networks.node_types[:, 0] == NODE_BIAS)  # All have bias nodes
    
    # Verify no memory issues with large populations
    assert jnp.isfinite(networks.node_types).all()
    assert jnp.isfinite(networks.activation_fns).all()

def test_create_networks_subset(test_config):
    """Test create_networks logic using convert_genomes_to_networks for a subset of genomes."""
    rng_key = jax.random.PRNGKey(123)
    tracker = InnovationTracker.create(test_config)
    pop_size = 6
    config = NEATConfig.create(
        population=PopulationConfig(population_size=pop_size, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=test_config.num_inputs,
            num_outputs=test_config.num_outputs,
            max_nodes=10,  # Use a smaller max_nodes value for testing
            max_connections=test_config.max_connections,
            activation_fn=test_config.activation_fn,
            output_activation=test_config.output_activation,
            hidden_activation=test_config.hidden_activation
        )
    )

    population, _ = initialize_population(
        key=rng_key,
        innovation_tracker=tracker,
        config=config
    )

    indices = jnp.array([1, 3, 5])
    connections = population.connections[indices]
    enabled = population.enabled[indices]

    networks, _ = convert_genomes_to_networks(
        connections=connections,
        enabled=enabled,
        config=config
    )

    batch_size = networks.node_types.shape[0]
    assert hasattr(networks, 'node_types')
    assert hasattr(networks, 'activation_fns')
    assert networks.node_types.shape == (batch_size, config.network.max_nodes)
    assert networks.activation_fns.shape == (batch_size, config.network.max_nodes)

# =============================================================================
# Tests for convert_genomes_to_networks
# =============================================================================

def test_convert_genomes_to_networks_basic():
    """Test convert_genomes_to_networks for correctness of node indices, types, and activation functions."""
    num_genomes = 2
    max_nodes = 8
    num_inputs = 2
    num_outputs = 2
    
    # Create test config
    config = NEATConfig.create(
        population=PopulationConfig(population_size=1, weight_init_std=1.0, weight_init_mean=0.0),
        network=NetworkConfig(
            num_inputs=num_inputs,
            num_outputs=num_outputs,
            max_nodes=max_nodes,
            activation_fn="tanh",
            hidden_activation="tanh",
            output_activation="sigmoid"
        )
    )
    
    # Create connections that use all input and output nodes
    connections = jnp.array([
        [
            [0, 2, 0.5, 1],  # input 0 to output 0
            [1, 3, -0.3, 2], # input 1 to output 1
            [2, 4, 1.0, 3],  # output 0 to hidden 0
            [3, 5, 1.0, 4],  # output 1 to hidden 1
        ],
        [
            [0, 3, 0.7, 1],  # input 0 to output 1
            [1, 2, 1.2, 2],  # input 1 to output 0
            [4, 5, 0.8, 3],  # hidden 0 to hidden 1
            [5, 6, 0.9, 4],  # hidden 1 to hidden 2
        ]
    ], dtype=jnp.float32)
    enabled = jnp.ones((num_genomes, 4), dtype=bool)
    networks, _ = convert_genomes_to_networks(
        connections=connections,
        enabled=enabled,
        config=config
    )
    # Check that node_indices are correct for each genome in the batch
    for i in range(num_genomes):
        assert jnp.array_equal(networks.node_indices[i], jnp.arange(max_nodes))
        # Node types: 0=bias, 1=input, 2=output, 3=hidden, 4=unused
        # Check bias node
        assert networks.node_types[i][0] == NODE_BIAS  # NODE_BIAS
        # Check input nodes
        assert (networks.node_types[i][1:num_inputs+1] == NODE_INPUT).all()  # NODE_INPUT
        # Check output nodes
        assert (networks.node_types[i][num_inputs+1:num_inputs+num_outputs+1] == NODE_OUTPUT).all()  # NODE_OUTPUT
        # Check hidden nodes (if any)
        hidden_start = num_inputs + num_outputs + 1
        if hidden_start < max_nodes:
            hidden_mask = (networks.node_types[i][hidden_start:] == NODE_HIDDEN)
            assert hidden_mask.sum() >= 0  # Allow zero or more hidden nodes
        # Activation functions: sigmoid (index 0) for used, -1 for unused
        used_mask = networks.node_types[i] != NODE_UNUSED
        assert (networks.activation_fns[i][used_mask] >= 0).all()  # Used nodes have valid activation
        assert (networks.activation_fns[i][networks.node_types[i] == NODE_UNUSED] == -1).all()  # Unused nodes have -1

# =============================================================================
# Tests for population initialization with bias
# =============================================================================

def test_population_bias_node_survives_mutation_and_crossover():
    """
    (Future) Test that bias node and bias->output connections are preserved during mutation and crossover.

    Steps to implement:
    1. Initialize a population and extract a genome/network.
    2. Apply mutation and/or crossover functions to the genome.
    3. After mutation/crossover:
       - Assert the bias node (index 0) is still present and of correct type.
       - Assert the bias node activation is still identity (7).
       - Assert every output node still receives a bias->output connection.
       - Assert all bias->output connection weights are still 1.0 (or, if mutation allows, within allowed bounds).
       - Assert no bias->non-output connections exist.
    4. Optionally, repeat for multiple generations or mutation/crossover types.
    """
    pass

if __name__ == "__main__":
    # Run all tests
    print("Running tests...")

    # Create test fixtures
    rng_key = jax.random.PRNGKey(0)
    tracker = InnovationTracker.create()

    # Population Initialization Tests
    print("\nPopulation Initialization Tests:")
    test_population_initialize_defaults(rng_key, test_config())
    test_population_initialize_custom(rng_key, test_config())
    test_get_genome_data(rng_key, test_config())
    test_update_genome(rng_key, test_config())
    test_replace(rng_key, test_config())

    # Innovation Tracking Tests
    print("\nInnovation Tracking Tests:")
    test_innovation_tracking_consistency(rng_key, test_config())
    test_dtype_integrity(test_config())

    # Network Creation/Conversion Tests
    print("\nNetwork Creation/Conversion Tests:")
    test_create_networks_subset(test_config())
    test_convert_genomes_to_networks_basic()

    # Bias Node and Connection Tests
    print("\nBias Node and Connection Tests:")
    test_population_initialization_with_bias(rng_key, test_config())

    # Uncomment when mutation/crossover logic is ready:
    test_population_bias_node_survives_mutation_and_crossover()

    print("\nAll tests passed!")