"""
Default NEAT configuration for general-purpose use with EvoJAX.

This module provides sensible default configurations for NEAT that work well
with the EvoJAX interface for various optimization tasks.
"""

from .config.base import (
    NetworkConfig,
    PopulationConfig,
    SpeciesConfig,
    RecombinationConfig,
    MutationConfig,
    FitnessConfig,
    BackpropConfig,
    NEATConfig
)

# Default network configuration - flexible for various input/output sizes
DEFAULT_NETWORK_CONFIG = NetworkConfig(
    num_inputs=2,           # Can be overridden when creating NEAT instance
    num_outputs=1,          # Can be overridden when creating NEAT instance
    max_nodes=100,          # Allow for reasonable complexity
    max_connections=200,    # Allow for dense connectivity
    hidden_activation="relu",
    output_activation="sigmoid"
)

# Default population configuration - balanced for performance and diversity
DEFAULT_POPULATION_CONFIG = PopulationConfig(
    population_size=150,    # Good balance of diversity and computational cost
    weight_init_std=0.5,    # Moderate weight initialization
    weight_init_mean=0.0
)

# Default species configuration - moderate speciation for diversity
DEFAULT_SPECIES_CONFIG = SpeciesConfig(
    compatibility_threshold=3.0,    # Moderate speciation
    gene_coefficient=1.0,
    weight_coefficient=0.5,
    max_species=20,                 # Allow reasonable number of species
    stagnation_threshold=15,        # Allow time for improvement
    rank_strategy="fitness"
)

# Default recombination configuration - balanced crossover
DEFAULT_RECOMBINATION_CONFIG = RecombinationConfig(
    tournament_size=3,              # Small tournament selection
    parent1_gene_rate=0.6,          # Slight bias toward fitter parent
    cull_ratio=0.5,                 # Keep top 50% for reproduction
    elite_ratio=0.1                 # Preserve top 10% as elites
)

# Default mutation configuration - moderate mutation rates
DEFAULT_MUTATION_CONFIG = MutationConfig(
    add_node_rate=0.03,             # Conservative structural growth
    add_connection_rate=0.05,       # Moderate connectivity increase
    shift_weight_rate=0.8,          # High probability of weight changes
    weight_scale=0.1,               # Small weight perturbations
    new_weight_std=1.0              # Standard deviation for new weights
)

# Default fitness configuration - no complexity penalties by default
DEFAULT_FITNESS_CONFIG = FitnessConfig(
    connection_cost=0.0,            # No penalty for connections
    node_cost=0.0                   # No penalty for nodes
)

# Default backpropagation configuration - disabled by default for pure NEAT
DEFAULT_BACKPROP_CONFIG = BackpropConfig(
    enabled=False,                  # Pure NEAT by default
    learning_rate=0.01,
    num_epochs=10,
    batch_size=32,
    rounds=1,
    gradient_clip=1.0,
    max_weight=5.0,
    max_errors=1000
)

# Complete default NEAT configuration
DEFAULT_NEAT_CONFIG = NEATConfig(
    # Core evolution parameters
    seed=0,
    max_generations=100,
    target_fitness=float('inf'),    # No target by default
    log_progress=True,
    log_frequency=10,

    # Training data (None for EvoJAX interface)
    inputs=None,
    targets=None,

    # Core configuration components
    network=DEFAULT_NETWORK_CONFIG,
    population=DEFAULT_POPULATION_CONFIG,
    species=DEFAULT_SPECIES_CONFIG,
    recombination=DEFAULT_RECOMBINATION_CONFIG,
    mutation=DEFAULT_MUTATION_CONFIG,
    fitness=DEFAULT_FITNESS_CONFIG,
    backprop=DEFAULT_BACKPROP_CONFIG
)

def create_neat_config(
    num_inputs: int = 2,
    num_outputs: int = 1,
    population_size: int = 150,
    max_generations: int = 100,
    **kwargs
) -> NEATConfig:
    """Create a NEAT configuration with custom parameters.
    
    Args:
        num_inputs: Number of input nodes
        num_outputs: Number of output nodes  
        population_size: Size of the population
        max_generations: Maximum number of generations
        **kwargs: Additional configuration parameters to override
        
    Returns:
        NEATConfig object with specified parameters
    """
    # Create custom network config
    network_config = DEFAULT_NETWORK_CONFIG.replace(
        num_inputs=num_inputs,
        num_outputs=num_outputs
    )
    
    # Create custom population config
    population_config = DEFAULT_POPULATION_CONFIG.replace(
        population_size=population_size
    )
    
    # Start with default config
    config = DEFAULT_NEAT_CONFIG.replace(
        network=network_config,
        population=population_config,
        max_generations=max_generations
    )
    
    # Apply any additional overrides
    for key, value in kwargs.items():
        if hasattr(config, key):
            config = config.replace(**{key: value})
        else:
            # Handle nested config updates
            if key.startswith('network_'):
                attr = key[8:]  # Remove 'network_' prefix
                if hasattr(config.network, attr):
                    new_network = config.network.replace(**{attr: value})
                    config = config.replace(network=new_network)
            elif key.startswith('population_'):
                attr = key[11:]  # Remove 'population_' prefix
                if hasattr(config.population, attr):
                    new_population = config.population.replace(**{attr: value})
                    config = config.replace(population=new_population)
            elif key.startswith('species_'):
                attr = key[8:]  # Remove 'species_' prefix
                if hasattr(config.species, attr):
                    new_species = config.species.replace(**{attr: value})
                    config = config.replace(species=new_species)
            elif key.startswith('mutation_'):
                attr = key[9:]  # Remove 'mutation_' prefix
                if hasattr(config.mutation, attr):
                    new_mutation = config.mutation.replace(**{attr: value})
                    config = config.replace(mutation=new_mutation)
    
    return config

# Export commonly used configurations
__all__ = [
    'DEFAULT_NEAT_CONFIG',
    'DEFAULT_NETWORK_CONFIG',
    'DEFAULT_POPULATION_CONFIG', 
    'DEFAULT_SPECIES_CONFIG',
    'DEFAULT_RECOMBINATION_CONFIG',
    'DEFAULT_MUTATION_CONFIG',
    'DEFAULT_FITNESS_CONFIG',
    'DEFAULT_BACKPROP_CONFIG',
    'create_neat_config'
]
