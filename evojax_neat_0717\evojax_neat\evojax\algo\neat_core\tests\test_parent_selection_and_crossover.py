import jax
import jax.numpy as jnp
import chex
import pytest
from typing import Tuple
from ..parent_selection_and_crossover import select_parents, recombine
from ..species import SpeciesState
from ..config.base import RecombinationConfig

# Helper: construct a minimal valid SpeciesState using the real dataclass

def make_species_state(pop_size, max_species=4, key=None):
    if key is None:
        key = jax.random.PRNGKey(0)
    return SpeciesState(
        species_ids=jnp.zeros(pop_size, dtype=jnp.int32),
        next_species_id=1,
        active_mask=jnp.ones(max_species, dtype=bool),
        member_masks=jnp.ones((max_species, pop_size), dtype=bool),
        representatives=jnp.zeros((max_species, 4)),  # shape matches dummy genome shape
        offspring_counts=jnp.ones(max_species, dtype=jnp.int32),
        best_fitnesses=jnp.zeros(max_species, dtype=jnp.float32),
        last_improvements=jnp.zeros(max_species, dtype=jnp.int32),
        rng_key=key
    )


def test_select_parents_shape():
    pop_size = 8
    ranks = jnp.arange(pop_size)
    key = jax.random.PRNGKey(0)
    config = RecombinationConfig()
    species_state = make_species_state(pop_size)
    parent_pairs = select_parents(species_state, ranks, key, config)
    assert parent_pairs.shape == (pop_size, 2)
    assert parent_pairs.dtype == jnp.int32 or parent_pairs.dtype == jnp.int64


def test_recombine_shapes_and_elitism():
    """Test that recombination maintains shapes and preserves elite individuals."""
    pop_size = 10
    max_connections = 5
    key = jax.random.PRNGKey(42)
    
    # Create test data with unique values to verify crossover
    population = jnp.tile(
        jnp.arange(max_connections * 4, dtype=jnp.float32).reshape(1, -1), 
        (pop_size, 1)
    ).reshape(pop_size, max_connections, 4)
    
    # Make each individual unique
    population = population * jnp.arange(1, pop_size + 1)[:, None, None].astype(jnp.float32)
    population_enabled = jnp.ones((pop_size, max_connections), dtype=bool)
    fitness = jnp.arange(pop_size, 0, -1, dtype=jnp.float32)  # Best fitness first
    species_state = make_species_state(pop_size)

    config = RecombinationConfig(
        elite_ratio=0.2,  # 2 elites for pop_size=10
        cull_ratio=0.3,   # Remove 30% worst
        tournament_size=3
    )
    
    new_pop, new_enabled = recombine(
        species_state=species_state,
        connections=population,
        enabled=population_enabled,
        fitness=fitness,
        key=key,
        config=config
    )
    
    # Shape validation
    assert new_pop.shape == population.shape, "Output population shape mismatch"
    assert new_enabled.shape == population_enabled.shape, "Output enabled mask shape mismatch"
    
    # Elitism check - best individuals should be preserved exactly
    elite_count = int(pop_size * config.elite_ratio)
    if elite_count > 0:
        assert jnp.array_equal(new_pop[:elite_count], population[:elite_count]), \
            "Elite individuals were not preserved"
        assert jnp.array_equal(new_enabled[:elite_count], population_enabled[:elite_count]), \
            "Elite individual masks were not preserved"
    
    # Check that disabled connections have zero weight
    disabled_weights = new_pop[~new_enabled]
    assert jnp.all(disabled_weights == 0), "Disabled connections should have zero weight"
    
    # Check that enabled connections are within valid range
    enabled_weights = new_pop[new_enabled]
    assert jnp.all(jnp.isfinite(enabled_weights)), "All weights must be finite"
    
    # Verify that new population contains valid combinations of parents
    # (This is a basic check - more sophisticated verification would be specific to your crossover implementation)
    assert not jnp.array_equal(new_pop, population), "Population should change due to crossover"


@pytest.mark.parametrize("pop_size,max_connections", [
    (2, 3),    # Minimal population
    (1, 1),    # Edge case: single individual
    (3, 10),   # More connections than population
])
def test_recombine_small_populations(pop_size: int, max_connections: int):
    """Test recombination with very small population sizes."""
    key = jax.random.PRNGKey(123)
    
    # Create unique individuals
    population = jnp.tile(
        jnp.arange(max_connections * 4, dtype=jnp.float32).reshape(1, -1),
        (pop_size, 1)
    ).reshape(pop_size, max_connections, 4)
    population = population * jnp.arange(1, pop_size + 1)[:, None, None].astype(jnp.float32)
    
    population_enabled = jnp.ones((pop_size, max_connections), dtype=bool)
    fitness = jnp.linspace(1.0, 0.1, num=pop_size, dtype=jnp.float32)  # Best first
    species_state = make_species_state(pop_size)

    config = RecombinationConfig(
        tournament_size=min(3, pop_size),  # Don't exceed population size
        elite_ratio=0.5 if pop_size > 1 else 1.0,  # Keep all if only one
        cull_ratio=0.5 if pop_size > 2 else 0.0  # Don't cull if too small
    )
    
    new_pop, new_enabled = recombine(
        species_state=species_state,
        connections=population,
        enabled=population_enabled,
        fitness=fitness,
        key=key,
        config=config
    )
    
    # Basic shape validation
    assert new_pop.shape == population.shape, f"Expected shape {population.shape}, got {new_pop.shape}"
    assert new_enabled.shape == population_enabled.shape, \
        f"Expected shape {population_enabled.shape}, got {new_enabled.shape}"
    
    # Check elitism (if applicable)
    if pop_size > 1 and config.elite_ratio > 0:
        elite_count = max(1, int(pop_size * config.elite_ratio))
        assert jnp.array_equal(
            new_pop[:elite_count], 
            population[:elite_count]
        ), f"Elite individuals not preserved in population of size {pop_size}"

# Comprehensive test cases that cover edge cases and biological validity
def test_recombine_basic_functionality():
    """Test basic recombination with elitism and culling"""
    key = jax.random.PRNGKey(0)
    pop_size = 10
    max_connections = 5
    
    # Create population with descending fitness (best first)
    population = jnp.arange(pop_size * max_connections * 4, dtype=jnp.float32).reshape(pop_size, max_connections, 4)
    enabled = jnp.ones((pop_size, max_connections), dtype=bool)
    fitness = jnp.arange(pop_size, 0, -1, dtype=jnp.float32)  # Best fitness first
    
    species_state = SpeciesState(
        member_masks=jnp.ones((3, pop_size), dtype=bool),
        offspring_counts=jnp.array([pop_size], dtype=jnp.int32),
        species_ids=jnp.arange(3),
        next_species_id=3,
        active_mask=jnp.ones(3, dtype=bool),
        representatives=population[:3],
        best_fitnesses=jnp.full(3, -jnp.inf),
        last_improvements=jnp.zeros(3, dtype=jnp.int32),
        rng_key=key
    )
    
    config = RecombinationConfig(
        elite_ratio=0.2,
        cull_ratio=0.3
    )
    new_pop, new_enabled = recombine(
        species_state=species_state,
        connections=population,
        enabled=enabled,
        fitness=fitness,
        key=key,
        config=config
    )
    
    # Basic shape checks
    chex.assert_shape(new_pop, population.shape)
    chex.assert_shape(new_enabled, enabled.shape)
    
    # Verify elites preserved (first 2 individuals)
    assert jnp.all(new_pop[0] == population[0]), "Top elite not preserved"
    assert jnp.all(new_pop[1] == population[1]), "Second elite not preserved"

def test_small_population_handling():
    """Test edge case with minimal population size"""
    key = jax.random.PRNGKey(1)
    pop_size = 5
    max_connections = 3
    
    population = jax.random.uniform(key, (pop_size, max_connections, 4))
    enabled = jnp.ones((pop_size, max_connections), dtype=bool)
    fitness = jnp.array([5, 4, 3, 2, 1], dtype=jnp.float32)
    
    species_state = SpeciesState(
        member_masks=jnp.ones((1, pop_size), dtype=bool),
        offspring_counts=jnp.array([pop_size], dtype=jnp.int32),
        species_ids=jnp.array([0]),
        next_species_id=1,
        active_mask=jnp.array([True]),
        representatives=population[0:1],
        best_fitnesses=jnp.full(1, -jnp.inf),
        last_improvements=jnp.zeros(1, dtype=jnp.int32),
        rng_key=key
    )
    
    config = RecombinationConfig(
        elite_ratio=0.2,  # 1 elite
        cull_ratio=0.4    # 2 culled
    )
    new_pop, _ = recombine(
        species_state=species_state,
        connections=population,
        enabled=enabled,
        fitness=fitness,
        key=key,
        config=config
    )
    
    # Verify elite preservation
    assert jnp.all(new_pop[0] == population[0]), "Single elite not preserved"

def test_max_culling_and_elitism():
    """Test extreme parameters (cull 75%, keep 50% of remaining)"""
    key = jax.random.PRNGKey(2)
    pop_size = 8
    population = jnp.arange(pop_size * 4 * 2, dtype=jnp.float32).reshape(pop_size, 4, 2)
    enabled = jnp.ones((pop_size, 4), dtype=bool)
    fitness = jnp.arange(pop_size, 0, -1, dtype=jnp.float32)  # 8 (best) to 1 (worst)
    
    species_state = SpeciesState(
        member_masks=jnp.ones((2, pop_size), dtype=bool),
        offspring_counts=jnp.array([4, 4], dtype=jnp.int32),
        species_ids=jnp.array([0, 1]),
        next_species_id=2,
        active_mask=jnp.array([True, True]),
        representatives=population[:2],
        best_fitnesses=jnp.full(2, -jnp.inf),
        last_improvements=jnp.zeros(2, dtype=jnp.int32),
        rng_key=key
    )
    
    config = RecombinationConfig(
        elite_ratio=0.5,  # 50% of survivors (2 elites)
        cull_ratio=0.75   # 6 culled (75% of 8)
    )
    new_pop, _ = recombine(
        species_state=species_state,
        connections=population,
        enabled=enabled,
        fitness=fitness,
        key=key,
        config=config
    )
    
    # Verify top 2 elites preserved from remaining survivors
    assert jnp.all(new_pop[0] == population[0]), "Top elite not preserved"
    assert jnp.all(new_pop[1] == population[1]), "Second elite not preserved"

def test_species_constraints():
    """Verify parents are selected within species boundaries"""
    key = jax.random.PRNGKey(3)
    pop_size = 6
    max_connections = 4
    
    # Create two distinct species
    species_masks = jnp.array([
        [True, True, True, False, False, False],  # Species 0
        [False, False, False, True, True, True]   # Species 1
    ])
    
    population = jax.random.normal(key, (pop_size, max_connections, 4))
    enabled = jnp.ones((pop_size, max_connections), dtype=bool)
    fitness = jnp.array([1.0, 2.0, 3.0, 4.0, 5.0, 6.0], dtype=jnp.float32)
    
    species_state = SpeciesState(
        member_masks=species_masks,
        offspring_counts=jnp.array([3, 3], dtype=jnp.int32),
        species_ids=jnp.array([0, 1]),
        next_species_id=2,
        active_mask=jnp.array([True, True]),
        representatives=population[:2],
        best_fitnesses=jnp.full(2, -jnp.inf),
        last_improvements=jnp.zeros(2, dtype=jnp.int32),
        rng_key=key
    )
    
    config = RecombinationConfig(tournament_size=2)
    parent_pairs = select_parents(
        species_state=species_state,
        ranks=jnp.argsort(fitness),  # Lower rank = better
        key=key,
        config=config
    )
    
    # Verify parents come from same species
    for pair in parent_pairs:
        p1, p2 = pair
        species1 = jnp.argmax(species_masks[:, p1])
        species2 = jnp.argmax(species_masks[:, p2])
        assert species1 == species2, "Parents cross species boundary"

def test_numerical_stability():
    """Test with identical fitness values and edge cases"""
    key = jax.random.PRNGKey(4)
    pop_size = 7
    population = jax.random.normal(key, (pop_size, 5, 4))
    enabled = jnp.ones((pop_size, 5), dtype=bool)
    
    # All same fitness
    fitness = jnp.ones(pop_size, dtype=jnp.float32)
    
    species_state = SpeciesState(
        member_masks=jnp.ones((1, pop_size), dtype=bool),
        offspring_counts=jnp.array([pop_size], dtype=jnp.int32),
        species_ids=jnp.array([0]),
        next_species_id=1,
        active_mask=jnp.array([True]),
        representatives=population[:1],
        best_fitnesses=jnp.full(1, -jnp.inf),
        last_improvements=jnp.zeros(1, dtype=jnp.int32),
        rng_key=key
    )
    
    # Should handle random selection gracefully
    config = RecombinationConfig(elite_ratio=0.0, cull_ratio=0.0)
    new_pop, new_enabled = recombine(
        species_state=species_state,
        connections=population,
        enabled=enabled,
        fitness=fitness,
        key=key,
        config=config
    )
    
    chex.assert_shape(new_pop, population.shape)
    chex.assert_shape(new_enabled, enabled.shape)