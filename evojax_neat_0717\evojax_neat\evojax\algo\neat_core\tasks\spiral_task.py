#!/usr/bin/env python3
"""
Spiral Classification Task - Hybrid NEAT Implementation

Binary classification task with two interleaved spirals. Points from one spiral
are labeled +1 and points from the other spiral are labeled -1. This is a
highly non-linear classification problem that requires complex decision boundaries.

Features:
- Configurable spiral parameters (radius, turns, noise)
- Professional logging and monitoring
- Detailed performance metrics
- Real-time progress tracking
- Visualization support

Usage: python spiral_task.py [--seed SEED] [--generations GENS] [--samples SAMPLES] [--noise NOISE] [--no-viz]
"""

import jax
import jax.numpy as jnp
import numpy as np
import time
import logging
import gc
import argparse
import math
from typing import Dict, Any, Optional
from dataclasses import dataclass

# Import NEAT components
from ..config.tasks.spiral import (
    SPIRAL_SIMPLE_CONFIG,
    SPIRAL_FAST_ULTRA_CONFIG,
    SPIRAL_ULTRA_AGGRESSIVE_CONFIG
)
from ..evolution import evolve_population
from ..innovation import InnovationTracker
from ..loss_fns import binary_cross_entropy
from ..population import initialize_population, convert_genomes_to_networks
from ..network import ActivationState, Network
from ..species import initialize_state
from ..visualization import create_visualization_hook, plot_fitness_history, visualize_network, plot_decision_boundary, plot_spiral_boundary

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

@dataclass
class SpiralDataset:
    """Container for spiral classification dataset"""
    inputs: jnp.ndarray
    targets: jnp.ndarray
    radius: float
    noise: float
    num_samples: int
    turns: float

    def __post_init__(self):
        """Validate dataset after initialization"""
        if self.inputs.shape[0] != self.targets.shape[0]:
            raise ValueError("Inputs and targets must have same number of samples")
        if self.inputs.shape[1] != 2:
            raise ValueError("Spiral task requires 2D inputs")
        if self.radius <= 0:
            raise ValueError("Radius must be positive")
        if self.turns <= 0:
            raise ValueError("Turns must be positive")

def generate_spiral_dataset(num_samples: int = 400, radius: float = 6.0, noise: float = 0.1,
                          turns: float = 1.75, seed: int = 42) -> SpiralDataset:
    """
    Generate spiral classification dataset with two interleaved spirals

    Args:
        num_samples: Total number of samples to generate
        radius: Maximum radius of the spirals
        noise: Noise level to add to coordinates
        turns: Number of turns in the spiral (1.75 is typical)
        seed: Random seed for reproducibility

    Returns:
        SpiralDataset: Generated dataset with inputs and targets
    """
    np.random.seed(seed)

    n = num_samples // 2  # Points per spiral

    def gen_spiral(delta_t: float, label: float):
        """Generate one spiral"""
        points = []
        labels = []

        for i in range(n):
            # Parametric spiral generation
            r = (i / n) * radius  # Radius grows linearly
            t = turns * (i / n) * 2 * math.pi + delta_t  # Angle with offset

            # Calculate spiral coordinates
            x = r * math.sin(t)
            y = r * math.cos(t)

            # Add noise
            x += np.random.uniform(-1, 1) * noise
            y += np.random.uniform(-1, 1) * noise

            points.append([x, y])
            labels.append([label])

        return points, labels

    # Generate two spirals with different starting angles
    points_pos, labels_pos = gen_spiral(0, 1.0)          # First spiral: +1
    points_neg, labels_neg = gen_spiral(math.pi, -1.0)   # Second spiral: -1 (offset by π)

    # Combine spirals
    all_points = points_pos + points_neg
    all_labels = labels_pos + labels_neg

    # Convert to JAX arrays
    inputs = jnp.array(all_points)
    targets = jnp.array(all_labels)

    # Shuffle the data
    indices = np.random.permutation(len(all_points))
    inputs = inputs[indices]
    targets = targets[indices]

    logger.info(f"Generated spiral dataset: {num_samples} samples, radius={radius:.1f}, noise={noise:.3f}, turns={turns:.2f}")

    # Log class distribution
    positive_count = jnp.sum(targets == 1.0)
    negative_count = jnp.sum(targets == -1.0)
    logger.info(f"Class distribution: {positive_count} spiral 1 (+1), {negative_count} spiral 2 (-1)")

    return SpiralDataset(
        inputs=inputs,
        targets=targets,
        radius=radius,
        noise=noise,
        num_samples=num_samples,
        turns=turns
    )

@dataclass
class EvolutionMetrics:
    """Container for evolution performance metrics"""
    generation: int
    current_fitness: float
    best_fitness: float
    best_generation: int
    population_diversity: float
    species_count: int
    elapsed_time: float
    breakthrough_generation: Optional[int] = None

    def __post_init__(self):
        """Validate metrics after initialization"""
        if self.generation < 0:
            raise ValueError("Generation must be non-negative")
        if self.elapsed_time < 0:
            raise ValueError("Elapsed time must be non-negative")

@dataclass
class FinalResults:
    """Container for final evolution results"""
    success: bool
    fitness: float
    accuracy: float
    generation: int
    total_time: float
    breakthrough_generation: Optional[int]
    test_outputs: Dict[str, Any]
    configuration_name: str
    dataset_info: Dict[str, Any]

# Global variables for the current dataset (will be set before evolution)
CURRENT_SPIRAL_INPUTS = None
CURRENT_SPIRAL_TARGETS = None

def evaluate_spiral_fitness(*args) -> float:
    """
    Spiral classification fitness function with robust sigmoid scaling

    Args:
        *args: Variable arguments supporting multiple calling conventions

    Returns:
        float: Fitness value (higher is better, range 0-10)
    """
    try:
        if len(args) == 2:
            predictions, targets = args
            # Use binary cross-entropy loss, convert to fitness with sigmoid scaling
            loss = binary_cross_entropy(predictions, targets)
            return 10.0 / (1.0 + loss)  # Robust sigmoid scaling: always positive, bounded 0-10
        elif len(args) == 3:
            network, activation_state, _ = args
            outputs, _ = network.forward(CURRENT_SPIRAL_INPUTS, activation_state)
            loss = binary_cross_entropy(outputs, CURRENT_SPIRAL_TARGETS)
            return 10.0 / (1.0 + loss)  # Same robust scaling
        else:
            raise ValueError(f"Invalid number of arguments: {len(args)}. Expected 2 or 3.")
    except Exception as e:
        logger.error(f"Error in fitness evaluation: {e}")
        return -jnp.inf

def calculate_spiral_accuracy(network: Network, activation_state: ActivationState, dataset: SpiralDataset) -> float:
    """
    Calculate spiral classification accuracy

    Args:
        network: Neural network to test
        activation_state: Network activation state
        dataset: Spiral dataset to test on

    Returns:
        float: Accuracy percentage (0-100)
    """
    try:
        outputs, _ = network.forward(dataset.inputs, activation_state)

        # Network outputs are already sigmoid-activated probabilities, no need for additional sigmoid
        probabilities = outputs  # Already probabilities from network's sigmoid output activation
        predictions = jnp.where(probabilities > 0.5, 1.0, -1.0)

        # Calculate accuracy
        correct = jnp.sum(predictions.flatten() == dataset.targets.flatten())
        accuracy = (correct / len(dataset.targets)) * 100.0

        return float(accuracy)
    except Exception as e:
        logger.error(f"Error calculating accuracy: {e}")
        return 0.0

def get_population_diversity(fitnesses: jnp.ndarray) -> float:
    """Calculate population diversity metric"""
    try:
        return float(jnp.std(fitnesses))
    except Exception:
        return 0.0

def create_spiral_config(max_generations: int = 100, dataset: SpiralDataset = None,
                        fast_ultra: bool = False, ultra: bool = False) -> Any:
    """
    Create configuration optimized for spiral classification

    Args:
        max_generations: Maximum number of generations
        dataset: Spiral dataset (for targets)
        fast_ultra: If True, use fast ultra config for speed + breakthrough
        ultra: If True, use ultra-aggressive config for maximum complexity
    """
    if ultra:
        base_config = SPIRAL_ULTRA_AGGRESSIVE_CONFIG
    elif fast_ultra:
        base_config = SPIRAL_FAST_ULTRA_CONFIG
    else:
        base_config = SPIRAL_SIMPLE_CONFIG

    return base_config.replace(
        targets=dataset.targets if dataset else None,
        max_generations=max_generations
    )

def log_generation_progress(metrics: EvolutionMetrics, config: Any) -> None:
    """Log detailed generation progress with production-quality formatting"""

    # Determine status emoji and message - spiral is harder, so lower thresholds
    if metrics.best_fitness >= 9.0:
        status = "🎉 EXCELLENT"
        color = "\033[92m"  # Green
    elif metrics.best_fitness >= 8.0:
        status = "✨ VERY GOOD"
        color = "\033[93m"  # Yellow
    elif metrics.best_fitness >= 6.5:
        status = "🔥 GOOD"
        color = "\033[96m"  # Cyan
    else:
        status = "🔄 EVOLVING"
        color = "\033[90m"  # Gray

    reset_color = "\033[0m"

    # Log main progress
    logger.info(
        f"{color}Gen {metrics.generation:2d}: "
        f"Current={metrics.current_fitness:.4f}, "
        f"Best={metrics.best_fitness:.4f} (Gen {metrics.best_generation}), "
        f"Species={metrics.species_count}, "
        f"Diversity={metrics.population_diversity:.3f}, "
        f"Time={metrics.elapsed_time:.1f}s - {status}{reset_color}"
    )

    # Log breakthrough moments
    if metrics.breakthrough_generation == metrics.generation:
        logger.info(f"🚀 BREAKTHROUGH at generation {metrics.generation}! Fitness: {metrics.best_fitness:.4f}")

def run_spiral_evolution(config: Any, dataset: SpiralDataset, seed: int = 42, enable_viz: bool = True) -> FinalResults:
    """
    Run spiral classification evolution with comprehensive monitoring

    Args:
        config: NEAT configuration
        dataset: Spiral dataset to train on
        seed: Random seed for reproducibility
        enable_viz: Whether to enable visualizations (default: True)

    Returns:
        FinalResults: Comprehensive results object
    """
    # Set global dataset variables for fitness function
    global CURRENT_SPIRAL_INPUTS, CURRENT_SPIRAL_TARGETS
    CURRENT_SPIRAL_INPUTS = dataset.inputs
    CURRENT_SPIRAL_TARGETS = dataset.targets

    logger.info("🚀 Starting Spiral Classification Evolution")
    logger.info(f"Dataset: {dataset.num_samples} samples, radius={dataset.radius:.1f}, noise={dataset.noise:.3f}, turns={dataset.turns:.2f}")
    logger.info(f"Population: {config.population.population_size}")
    logger.info(f"Max Generations: {config.max_generations}")
    logger.info(f"Backprop: {config.backprop.rounds} rounds @ LR {config.backprop.learning_rate}")
    logger.info(f"Random Seed: {seed}")
    logger.info(f"JAX Backend: {jax.default_backend()}")
    logger.info("-" * 60)

    start_time = time.time()

    # Initialize evolution components
    key = jax.random.PRNGKey(seed)
    tracker = InnovationTracker.create(config.network)

    key, init_key = jax.random.split(key)
    population, tracker = initialize_population(
        key=init_key,
        innovation_tracker=tracker,
        config=config
    )

    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=config
    )

    key, species_key = jax.random.split(key)
    species_state = initialize_state(
        connections=population.connections,
        key=species_key,
        config=config.species
    )

    # Create results directory and setup visualization
    import os
    results_dir = "results/spiral"
    viz_hook = None

    if enable_viz:
        os.makedirs(results_dir, exist_ok=True)

        # Create visualization hook
        viz_hook = create_visualization_hook(
            output_dir=results_dir,
            plot_interval=10,     # Plot every 10 generations
            network_interval=20,  # Network visualization every 20 generations
            plot_species=True,    # Include species count
            max_networks_per_gen=1,  # Just the best network
            show_weights=True,    # Show connection weights
            format='png'          # PNG format
        )

    # Evolution tracking
    best_fitness = -jnp.inf
    best_network_data = None
    best_generation = -1
    breakthrough_generation = None
    fitness_history = []
    species_history = []

    logger.info("Evolution started...")
    if enable_viz:
        logger.info(f"📊 Visualizations will be saved to: {results_dir}")
    else:
        logger.info("📊 Visualizations disabled for faster execution")

    # Use the global fitness function
    fitness_fn = evaluate_spiral_fitness

    # Main evolution loop
    for generation in range(config.max_generations):
        gen_start_time = time.time()

        # Evolve population
        networks, species_state, tracker, fitnesses, key = evolve_population(
            networks=networks,
            species_state=species_state,
            tracker=tracker,
            key=key,
            fitness_fn=fitness_fn,
            inputs=dataset.inputs,
            config=config
        )

        # Calculate metrics
        current_best_idx = jnp.argmax(fitnesses)
        current_best_fitness = float(fitnesses[current_best_idx])
        population_diversity = get_population_diversity(fitnesses)
        species_count = len(jnp.unique(species_state.species_ids))
        gen_elapsed_time = time.time() - gen_start_time

        # Check for improvement
        if current_best_fitness > best_fitness:
            best_fitness = current_best_fitness
            best_generation = generation

            # Store best network data
            best_network_data = {
                'connections': networks.connections[current_best_idx].copy(),
                'enabled': networks.enabled[current_best_idx].copy(),
                'node_types': networks.node_types[current_best_idx].copy(),
                'activation_fns': networks.activation_fns[current_best_idx].copy(),
            }

            # Check for breakthrough (first time > 7.0 - spiral is harder)
            if best_fitness > 7.0 and breakthrough_generation is None:
                breakthrough_generation = generation

        # Create metrics object
        metrics = EvolutionMetrics(
            generation=generation,
            current_fitness=current_best_fitness,
            best_fitness=best_fitness,
            best_generation=best_generation,
            population_diversity=population_diversity,
            species_count=species_count,
            elapsed_time=gen_elapsed_time,
            breakthrough_generation=breakthrough_generation if breakthrough_generation == generation else None
        )

        # Update history for visualization
        fitness_history.append(current_best_fitness)
        species_history.append(species_count)

        # Call visualization hook
        if enable_viz and viz_hook is not None:
            try:
                viz_hook(
                    generation=generation,
                    networks=networks,
                    species_state=species_state,
                    fitnesses=fitnesses,
                    best_fitness=current_best_fitness,
                    best_idx=current_best_idx
                )
            except Exception as e:
                logger.warning(f"⚠️  Visualization error: {e}")

        # Log progress
        log_generation_progress(metrics, config)

        # Early termination for excellent solution (spiral is harder, so lower threshold)
        if best_fitness >= 8.5:
            logger.info(f"🎉 EXCELLENT SOLUTION ACHIEVED! Terminating early at generation {generation}")
            break

    total_time = time.time() - start_time

    # Generate final visualization summary
    if enable_viz:
        try:
            # Final fitness history plot
            plot_fitness_history(
                fitness_history=jnp.array(fitness_history),
                species_counts=jnp.array(species_history),
                title=f"Spiral Evolution: Classification (Seed {seed})",
                save_path=f"{results_dir}/final_fitness_history.png",
                show=False
            )

            if len(fitness_history) > 0:
                logger.info(f"📊 Generated {len(fitness_history)} generation plots + final summary")
                logger.info(f"🎨 Visualization files saved to: {results_dir}")
        except Exception as e:
            logger.warning(f"⚠️  Final visualization error: {e}")

    # Reconstruct best network and evaluate
    if best_network_data is None:
        logger.error("❌ No valid network found during evolution!")
        return FinalResults(
            success=False, fitness=0.0, accuracy=0.0, generation=-1,
            total_time=total_time, breakthrough_generation=None,
            test_outputs={}, configuration_name="Spiral Classification",
            dataset_info={"samples": dataset.num_samples, "radius": dataset.radius,
                         "noise": dataset.noise, "turns": dataset.turns}
        )

    # Create best network
    best_network = Network(
        connections=best_network_data['connections'],
        enabled=best_network_data['enabled'],
        node_types=best_network_data['node_types'],
        activation_fns=best_network_data['activation_fns'],
        num_inputs=config.network.num_inputs,
        num_outputs=config.network.num_outputs,
        max_nodes=config.network.max_nodes
    )

    activation_state = ActivationState(
        node_depths=jnp.full(config.network.max_nodes, -1, dtype=jnp.int32),
        outdated_depths=True
    )

    # Visualize the best network
    if enable_viz:
        try:
            visualize_network(
                network=best_network,
                title=f"Best Spiral Network (Gen {best_generation}, Fitness {best_fitness:.4f})",
                save_path=f"{results_dir}/best_network_final",
                format='png'
            )
            logger.info(f"🎨 Best network visualization saved")
        except Exception as e:
            logger.warning(f"⚠️  Best network visualization error: {e}")

        # Visualize decision boundary
        try:
            plot_decision_boundary(
                network=best_network,
                dataset_inputs=dataset.inputs,
                dataset_targets=dataset.targets,
                title=f"Spiral Decision Boundary (Gen {best_generation}, Fitness {best_fitness:.4f})",
                true_boundary_fn=plot_spiral_boundary,
                boundary_params={'radius': dataset.radius, 'turns': dataset.turns},
                save_path=f"{results_dir}/decision_boundary.png",
                show=False,
                resolution=200
            )
            logger.info(f"🎨 Decision boundary visualization saved")
        except Exception as e:
            logger.warning(f"⚠️  Decision boundary visualization error: {e}")

    # Calculate final accuracy
    accuracy = calculate_spiral_accuracy(best_network, activation_state, dataset)

    # Test on sample points for detailed analysis
    test_outputs = {}
    sample_indices = [0, len(dataset.inputs)//4, len(dataset.inputs)//2, 3*len(dataset.inputs)//4, len(dataset.inputs)-1]

    for i, idx in enumerate(sample_indices):
        if idx >= len(dataset.inputs) or idx < 0:
            continue
        try:
            input_point = dataset.inputs[idx:idx+1]
            expected = dataset.targets[idx][0]
            output, _ = best_network.forward(input_point, activation_state)

            # Check if output is valid
            if output.size == 0:
                logger.warning(f"Empty output for sample {idx}, skipping")
                continue

            actual = float(output[0, 0])
            probability = float(output[0, 0])  # Already sigmoid-activated, no need for double sigmoid
            predicted_class = 1.0 if probability > 0.5 else -1.0
            correct = predicted_class == expected

            test_outputs[f"Sample_{i+1}"] = {
                'input': [float(input_point[0, 0]), float(input_point[0, 1])],
                'expected': float(expected),
                'raw_output': actual,
                'probability': probability,
                'predicted_class': float(predicted_class),
                'correct': correct
            }
        except Exception as e:
            logger.warning(f"Error processing sample {idx}: {e}")
            continue

    return FinalResults(
        success=best_fitness >= 6.5,  # Adjusted for sigmoid scaling (6.5 = good performance for spiral)
        fitness=best_fitness,
        accuracy=accuracy,
        generation=best_generation,
        total_time=total_time,
        breakthrough_generation=breakthrough_generation,
        test_outputs=test_outputs,
        configuration_name="Spiral Classification",
        dataset_info={"samples": dataset.num_samples, "radius": dataset.radius,
                     "noise": dataset.noise, "turns": dataset.turns}
    )

def print_final_results(results: FinalResults) -> None:
    """Print comprehensive final results with production formatting"""

    print("\n" + "=" * 80)
    print("🌀 SPIRAL CLASSIFICATION EVOLUTION RESULTS")
    print("=" * 80)

    # Success status
    if results.success:
        status_emoji = "🎉"
        status_text = "SUCCESS - EXCELLENT SOLUTION"
        status_color = "\033[92m"  # Green
    elif results.accuracy >= 70:  # Lower threshold for spiral
        status_emoji = "✅"
        status_text = "GOOD RESULT"
        status_color = "\033[93m"  # Yellow
    else:
        status_emoji = "⚠️"
        status_text = "SUBOPTIMAL RESULT"
        status_color = "\033[91m"  # Red

    reset_color = "\033[0m"

    print(f"{status_color}{status_emoji} {status_text}{reset_color}")
    print()

    # Core metrics
    print(f"📊 Performance Metrics:")
    print(f"   Fitness Score:      {results.fitness:.6f} / 10.000000")
    print(f"   Classification:     {results.accuracy:.1f}% accuracy")
    print(f"   Convergence:        Generation {results.generation}")
    print(f"   Total Runtime:      {results.total_time:.2f} seconds")

    if results.breakthrough_generation is not None:
        print(f"   Breakthrough:       Generation {results.breakthrough_generation}")

    print(f"   Configuration:      {results.configuration_name}")
    print()

    # Dataset info
    print(f"🔍 Dataset Information:")
    print(f"   Samples:            {results.dataset_info['samples']}")
    print(f"   Spiral Radius:      {results.dataset_info['radius']:.1f}")
    print(f"   Spiral Turns:       {results.dataset_info['turns']:.2f}")
    print(f"   Noise Level:        {results.dataset_info['noise']:.3f}")
    print()

    # Sample test cases
    print(f"🔍 Sample Test Cases:")
    print(f"   {'Sample':<10} {'Input (x,y)':<15} {'Expected':<10} {'Predicted':<10} {'Prob':<8} {'Status'}")
    print(f"   {'-'*10} {'-'*15} {'-'*10} {'-'*10} {'-'*8} {'-'*6}")

    for sample_name, sample_data in results.test_outputs.items():
        status = "✓ PASS" if sample_data['correct'] else "✗ FAIL"
        status_color = "\033[92m" if sample_data['correct'] else "\033[91m"

        input_str = f"({sample_data['input'][0]:+.2f},{sample_data['input'][1]:+.2f})"

        print(f"   {sample_name:<10} {input_str:<15} {sample_data['expected']:>+8.1f}  "
              f"{sample_data['predicted_class']:>+8.1f}  {sample_data['probability']:>6.3f}  "
              f"{status_color}{status}{reset_color}")

    print()

    # Performance analysis
    print(f"⚡ Performance Analysis:")
    efficiency_rating = "EXCELLENT" if results.generation <= 30 else "GOOD" if results.generation <= 60 else "MODERATE"
    print(f"   Convergence Speed:  {efficiency_rating} ({results.generation} generations)")
    print(f"   Solution Quality:   {'EXCELLENT' if results.fitness >= 8.5 else 'GOOD' if results.fitness >= 7.0 else 'MODERATE'}")
    print(f"   Runtime Efficiency: {results.total_time/max(results.generation, 1):.2f}s per generation")

    if results.success:
        print(f"\n🌀 Spiral classification is one of the most challenging 2D classification problems!")
        print(f"   The network successfully learned to distinguish between interleaved spirals.")
        print(f"   This demonstrates NEAT's ability to evolve complex non-linear decision boundaries.")

def cleanup_resources() -> None:
    """Clean up computational resources"""
    try:
        jax.clear_caches()
        gc.collect()
        logger.info("🧹 Resources cleaned up successfully")
    except Exception as e:
        logger.warning(f"⚠️ Resource cleanup warning: {e}")

def main():
    """Main execution function with argument parsing"""
    parser = argparse.ArgumentParser(description="Spiral Classification with Hybrid NEAT")
    parser.add_argument('--seed', type=int, default=42, help='Random seed (default: 42)')
    parser.add_argument('--generations', type=int, default=100, help='Max generations (default: 100)')
    parser.add_argument('--samples', type=int, default=400, help='Number of dataset samples (default: 400)')
    parser.add_argument('--radius', type=float, default=6.0, help='Spiral radius (default: 6.0)')
    parser.add_argument('--turns', type=float, default=1.75, help='Spiral turns (default: 1.75)')
    parser.add_argument('--noise', type=float, default=0.1, help='Noise level (default: 0.1)')
    parser.add_argument('--fast-ultra', action='store_true', help='Use fast ultra config for speed + breakthrough (recommended)')
    parser.add_argument('--ultra', action='store_true', help='Use ultra-aggressive config for maximum complexity (overrides --fast-ultra)')
    parser.add_argument('--no-viz', action='store_true', help='Disable visualizations for faster execution')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Generate dataset
        dataset = generate_spiral_dataset(
            num_samples=args.samples,
            radius=args.radius,
            noise=args.noise,
            turns=args.turns,
            seed=args.seed
        )

        # Create configuration
        fast_ultra = getattr(args, 'fast_ultra', False)
        config = create_spiral_config(args.generations, dataset, fast_ultra=fast_ultra, ultra=args.ultra)

        # Log configuration choice
        if args.ultra:
            config_name = "Ultra-Aggressive"
            logger.info(f"🔧 Using {config_name} configuration")
            logger.info("🚀 Ultra-aggressive config for maximum spiral complexity!")
            logger.info("   Population: 350, Max nodes: 150, Backprop: 20 rounds, Connection cost: 0.002, Node cost: 0.004")
        elif fast_ultra:
            config_name = "Fast Ultra"
            logger.info(f"🔧 Using {config_name} configuration")
            logger.info("⚡ Fast ultra config optimized for speed + breakthrough!")
            logger.info("   Population: 250, Max nodes: 100, Backprop: 15 rounds, Connection cost: 0.004, Node cost: 0.008")
        else:
            config_name = "Simple"
            logger.info(f"🔧 Using {config_name} configuration")

        # Run evolution (enable_viz is opposite of no_viz)
        results = run_spiral_evolution(config, dataset, args.seed, enable_viz=not args.no_viz)

        # Display results
        print_final_results(results)

        # Return appropriate exit code
        return 0 if results.success else 1

    except Exception as e:
        logger.error(f"❌ Evolution failed: {e}")
        raise
    finally:
        cleanup_resources()

if __name__ == "__main__":
    exit(main())