"""
Simple test to verify the fitness function fix works correctly.
"""

import jax
import jax.numpy as jnp
import sys
import os

# Add the current directory to the path so we can import the modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from evojax.algo.neat_core.config import XOR_CONFIG
    from evojax.algo.neat_core.network import ActivationState, extract_single_network
    from evojax.algo.neat_core.loss_fns import mean_squared_error
    from evojax.algo.neat_core import initialize_population, InnovationTracker, convert_genomes_to_networks
    
    print("✓ All imports successful")
    
    # XOR dataset
    XOR_INPUTS = jnp.array([[-1.0, -1.0], [-1.0, 1.0], [1.0, -1.0], [1.0, 1.0]])
    XOR_TARGETS = jnp.array([[-1.0], [1.0], [1.0], [-1.0]])
    
    def evaluate_xor_fitness(*args) -> float:
        """Test fitness function"""
        try:
            if len(args) == 2:
                predictions, targets = args
                mse = mean_squared_error(predictions, targets)
                return 4.0 - mse
            elif len(args) == 3:
                network, activation_state, _ = args
                outputs, _ = network.forward(XOR_INPUTS, activation_state)
                mse = mean_squared_error(outputs, XOR_TARGETS)
                return 4.0 - mse
            else:
                raise ValueError(f"Invalid number of arguments: {len(args)}. Expected 2 or 3.")
        except Exception as e:
            print(f"Error in fitness evaluation: {e}")
            return -jnp.inf
    
    # Test 1: Create a simple network and test fitness evaluation
    print("\n🧪 Testing fitness function...")
    
    # Initialize a small population
    key = jax.random.PRNGKey(42)
    tracker = InnovationTracker.create(XOR_CONFIG.network)
    
    key, init_key = jax.random.split(key)
    population, tracker = initialize_population(
        key=init_key,
        innovation_tracker=tracker,
        config=XOR_CONFIG
    )
    
    # Convert to networks
    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=XOR_CONFIG
    )
    
    print(f"✓ Created population with {networks.connections.shape[0]} networks")
    
    # Test fitness evaluation on first network
    single_network = extract_single_network(networks, 0)
    activation_state = ActivationState(
        node_depths=jnp.full(single_network.max_nodes, -1, dtype=jnp.int32),
        outdated_depths=True
    )
    
    # Test the 3-argument calling convention
    fitness = evaluate_xor_fitness(single_network, activation_state, XOR_INPUTS)
    print(f"✓ Fitness evaluation successful: {fitness:.6f}")
    
    # Test batch evaluation
    def evaluate_batch_fitness(networks):
        """Evaluate fitness for batch of networks"""
        batch_size = networks.connections.shape[0]
        fitness_scores = []
        
        for i in range(min(5, batch_size)):  # Test only first 5 networks
            single_network = extract_single_network(networks, i)
            activation_state = ActivationState(
                node_depths=jnp.full(single_network.max_nodes, -1, dtype=jnp.int32),
                outdated_depths=True
            )
            fitness = evaluate_xor_fitness(single_network, activation_state, XOR_INPUTS)
            fitness_scores.append(fitness)
        
        return jnp.array(fitness_scores)
    
    batch_fitness = evaluate_batch_fitness(networks)
    print(f"✓ Batch fitness evaluation successful: {batch_fitness}")
    print(f"  Mean fitness: {jnp.mean(batch_fitness):.6f}")
    print(f"  Best fitness: {jnp.max(batch_fitness):.6f}")
    
    print("\n🎉 All tests passed! The fitness function fix is working correctly.")
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("Make sure you're running this from the correct directory with the NEAT modules available.")
except Exception as e:
    print(f"❌ Test failed: {e}")
    import traceback
    traceback.print_exc()
