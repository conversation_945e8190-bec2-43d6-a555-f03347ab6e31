"""
Circle classification task-specific configuration for the NEAT algorithm.

This module provides the configuration for the circle classification problem.
Points inside a circle are labeled +1 and points outside are labeled -1.
"""
from ..base import (
    PopulationConfig,
    NetworkConfig,
    SpeciesConfig,
    RecombinationConfig,
    MutationConfig,
    FitnessConfig,
    BackpropConfig,
    NEATConfig
)

# --- Circle Configuration ---

# Population configuration for circle classification
CIRCLE_POPULATION_CONFIG = PopulationConfig(
    population_size=200,  # Optimized for circle complexity
    weight_init_std=2.5,  # Good initial weight variance
    weight_init_mean=0.0
)

# Network configuration for circle classification
CIRCLE_NETWORK_CONFIG = NetworkConfig(
    num_inputs=2,  # Circle task has 2D inputs (x, y)
    num_outputs=1,  # Binary classification
    max_nodes=75,         # High complexity for precise circular boundaries
    max_connections=300,  # High connections for circular boundary learning
    activation_fn="tanh",
    output_activation="sigmoid",  # Sigmoid for binary classification
    hidden_activation="tanh"
)

# Species configuration for circle - tuned to start with ≤15 species
CIRCLE_SPECIES_CONFIG = SpeciesConfig(
    max_species=15,              # Limit to at most 15 species initially
    gene_coefficient=1.0,        # Standard structural penalty for speciation
    weight_coefficient=1.0,      # High weight sensitivity for speciation
    compatibility_threshold=1.0, # Looser threshold to cluster genomes together
    stagnation_threshold=18,     # Good stagnation tolerance
    rank_strategy=1
)

# Recombination configuration for circle
CIRCLE_RECOMBINATION_CONFIG = RecombinationConfig(
    tournament_size=3,
    parent1_gene_rate=0.5,
    elite_ratio=0.05,  # Small elite ratio
    cull_ratio=0.5     # Moderate selection pressure
)

# Mutation configuration for circle - optimized for breakthrough performance
CIRCLE_MUTATION_CONFIG = MutationConfig(
    add_node_rate=0.5,      # High structural mutation for complex boundaries
    add_connection_rate=0.7, # High connection rate for circular learning
    shift_weight_rate=0.9,   # High weight mutation for fine-tuning
    weight_scale=3.5         # Good weight changes for boundary precision
)

# Fitness configuration for circle - balanced complexity penalty
CIRCLE_FITNESS_CONFIG = FitnessConfig(
    connection_cost=0.006,  # Balanced penalty - slightly higher than ultra
    node_cost=0.012         # Balanced node penalty
)

# Backpropagation configuration for circle - optimized for breakthrough
CIRCLE_BACKPROP_CONFIG = BackpropConfig(
    enabled=True,
    rounds=12,           # Optimized backprop rounds
    learning_rate=0.07,  # Higher learning rate for faster convergence
    gradient_clip=2.5,   # Good gradient clipping
    max_errors=70,       # Moderate error tolerance
    num_epochs=5,        # Good epochs
    batch_size=40,       # Optimized batch size
    max_weight=18.0      # Higher max weight for complex boundaries
)

# Main circle configuration
CIRCLE_CONFIG = NEATConfig(
    # Core evolution parameters
    seed=42,
    max_generations=20,   # Optimized for production use
    target_fitness=4.5,   # High target for good performance
    log_progress=True,
    log_frequency=2,      # Frequent logging for monitoring

    # Sub-configurations
    population=CIRCLE_POPULATION_CONFIG,
    network=CIRCLE_NETWORK_CONFIG,
    species=CIRCLE_SPECIES_CONFIG,
    recombination=CIRCLE_RECOMBINATION_CONFIG,
    mutation=CIRCLE_MUTATION_CONFIG,
    fitness=CIRCLE_FITNESS_CONFIG,
    backprop=CIRCLE_BACKPROP_CONFIG
)