es_name: "ARS"
problem_type: "mnist"
normalize: false
es_config:
  elite_ratio: 0.1
  pop_size: 100
  init_stdev: 0.03
  decay_stdev: 0.999
  limit_stdev: 0.01
  optimizer: "sgd"
  optimizer_config:
    lrate_init: 0.01
    lrate_decay: 0.999
    lrate_limit: 0.001
    momentum: 0.0
hidden_size: 100
batch_size: 1024
max_iter: 2000
test_interval: 500
log_interval: 100
num_tests: 1
n_repeats: 1
seed: 42
gpu_id: [0]
debug: false
