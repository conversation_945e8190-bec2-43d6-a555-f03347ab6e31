The following table summarizes the available neuroevolution algorithms and their performance.  
We hope this helps EvoJAX users choose the appropriate ones for their experiments.

| Algorithms                                                                                                                                             | Description                                                                                                                                                                                                                                  | Contributors                                    | Performance                                                          |
|--------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------|----------------------------------------------------------------------|
| [ARS](https://arxiv.org/pdf/1803.07055.pdf)                                                                                                            | This is a wrapper of the ARS implementation in [evosax](https://github.com/RobertTLange/evosax). ([source](https://github.com/google/evojax/blob/main/evojax/algo/ars.py))                                                                   | [RobertTLange](https://github.com/RobertTLange)  | [PR](https://github.com/google/evojax/pull/9#issue-1143656302)       |
| [ARS_native](https://arxiv.org/abs/1803.07055)                                                                                                            | Native jax implementation of Augmented Random Search. ([source](https://github.com/google/evojax/blob/main/evojax/algo/ars_native.py))                                                                   | [EdoardoPona](https://github.com/EdoardoPona)  | [PR](https://github.com/google/evojax/pull/47)       |
| [CMA-ES](https://arxiv.org/abs/1604.00772)                                                                                                             | This is a wrapper of the [original](https://github.com/CMA-ES/pycma) implementation. Notice that this runs on CPUs. ([source](https://github.com/google/evojax/blob/main/evojax/algo/cma_wrapper.py))                                        | EvoJAX team | TODO                                                                | 
| [CMA-ES](https://arxiv.org/abs/1604.00772)                                                                                                             | This is a wrapper of the CMA-ES implementation in [evosax](https://github.com/RobertTLange/evosax). ([source](https://github.com/google/evojax/blob/main/evojax/algo/cma_evosax.py))                                        | [RobertTLange](https://github.com/RobertTLange) | [PR](https://github.com/google/evojax/pull/21)                                                                | 
| [CMA-ES](https://arxiv.org/abs/1604.00772)                                                                                                             | This is a wrapper of the Sep-CMA-ES implementation in [evosax](https://github.com/RobertTLange/evosax). It runs fast but the covariance matrix in this implementation is diagonal. ([source](https://github.com/google/evojax/blob/main/evojax/algo/sep_cma_es.py))                     | [RobertTLange](https://github.com/RobertTLange) | [PR](https://github.com/google/evojax/pull/20)                                                                | 
| [CMA-ES](https://arxiv.org/abs/1604.00772)                                                                                                             | This is a CMA-ES optimizer using JAX backend, adpated from [this](https://github.com/CyberAgentAILab/cmaes/blob/main/cmaes/_cma.py) faithful implementation of the original CMA-ES algorithm. ([source](https://github.com/google/evojax/blob/main/evojax/algo/cma_jax.py))                     | EvoJAX Team | [PR](https://github.com/google/evojax/pull/32)                                                                | 
| [CR-FM-NES](https://arxiv.org/abs/2201.11422)                                                                                                             | This is a CR-FM-NES optimizer using JAX backend, adapted from [this](https://github.com/dietmarwo/fast-cma-es/blob/master/fcmaes/crfmnes.py) implementation of the original CR-FM-NES algorithm. ([performance](https://github.com/dietmarwo/fast-cma-es/blob/master/tutorials/EvoJax.adoc)) ([source](https://github.com/google/evojax/blob/main/evojax/algo/crfmnes.py))                    | [dietmarwo](https://github.com/dietmarwo) | [PR](https://github.com/google/evojax/pull/46)
| [CR-FM-NES](https://arxiv.org/abs/2201.11422)                                                                                                             | This is a wrapper of the CR-FM-NES C++/Eigen implementation from [fcmaes](https://github.com/dietmarwo/fast-cma-es), using [this](https://github.com/dietmarwo/fast-cma-es/blob/master/_fcmaescpp/crfmnes.cpp) implementation of the original CR-FM-NES algorithm. ([performance](https://github.com/dietmarwo/fast-cma-es/blob/master/tutorials/EvoJax.adoc)) ([source](https://github.com/google/evojax/blob/main/evojax/algo/fcrfmc.py))                     | [dietmarwo](https://github.com/dietmarwo) | [PR](https://github.com/google/evojax/pull/44)
| [OpenES](https://arxiv.org/pdf/1703.03864.pdf)                                                                                                    | This is a wrapper of the OpenES implementation in [evosax](https://github.com/RobertTLange/evosax). ([source](https://github.com/google/evojax/blob/main/evojax/algo/open_es.py)) | [RobertTLange](https://github.com/RobertTLange)                                     | [Table](https://github.com/google/evojax/tree/main/scripts/benchmarks#openes)             |                                                    |
| [PGPE](https://people.idsia.ch/~juergen/nn2010.pdf)                                                                                                    | This implementation is well tested, all examples are based on this algorithm. We provide two optimizers: Adam and [ClipUp](https://github.com/nnaisense/pgpelib). ([source](https://github.com/google/evojax/blob/main/evojax/algo/pgpe.py)) | EvoJAX team                                     | [Table](https://github.com/google/evojax/tree/main/scripts/benchmarks#pgpe)             |                                                    |
| [PGPE](https://people.idsia.ch/~juergen/nn2010.pdf)                                                                                                    | This is a wrapper of the PGPE C++/Eigen implementation from [fcmaes](https://github.com/dietmarwo/fast-cma-es), using [this](https://github.com/dietmarwo/fast-cma-es/blob/master/_fcmaescpp/pgpe.cpp) implementation of the original PGPE algorithm. We provide one optimizer: Adam. ([performance](https://github.com/dietmarwo/fast-cma-es/blob/master/tutorials/EvoJax.adoc)) ([source](https://github.com/google/evojax/blob/main/evojax/algo/fpgpec.py))                     | [dietmarwo](https://github.com/dietmarwo) | [PR](https://github.com/google/evojax/pull/51)
| [SimpleGA](http://cognet.mit.edu/book/simple-genetic-algorithm#:~:text=The%20Simple%20Genetic%20Algorithm%20(SGA,objects%20related%20to%20the%20SGA.)) | This implementation has truncation selection but no crossover. ([source](https://github.com/google/evojax/blob/main/evojax/algo/simple_ga.py))                                                                                                                                                                  | [MaximilienLC](https://github.com/MaximilienLC) | [PR](https://github.com/google/evojax/pull/5#issuecomment-1043879609) |
| [MAP-Elites](https://arxiv.org/abs/1504.04909) | This implementation uses [iso-line variantion](https://arxiv.org/abs/1804.03906) as the emitter. ([source](https://github.com/google/evojax/blob/main/evojax/algo/map_elites.py))                                                                                                                                                                  | EvoJAX team | [PR](https://github.com/google/evojax/pull/33) |
