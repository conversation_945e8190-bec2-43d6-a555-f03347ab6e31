"""
Core constants for the NEAT neural network implementation.
These values define internal types, states, and default configurations.
"""

# ===== Node Type Constants =====
# Values representing different types of nodes in the neural network
NODE_BIAS: int = 0    # Bias node (constant input)
NODE_INPUT: int = 1   # Input node (receives external input)
NODE_OUTPUT: int = 2  # Output node (produces network output)
NODE_HIDDEN: int = 3  # Hidden node (internal processing)
NODE_UNUSED: int = 4  # Unused/disabled node

# ===== Special Values =====
EMPTY_SLOT: int = -1  # Marks disabled connections in the network

# ===== Activation Function Constants =====
# Indices for activation functions in activation_fns_list
ACTIVATION_SIGMOID: int = 0    # Sigmoid activation (default)
ACTIVATION_TANH: int = 1       # Tanh activation
ACTIVATION_SOFTMAX: int = 2    # Softmax activation
ACTIVATION_RELU: int = 3       # ReLU activation  
ACTIVATION_LEAKY_RELU: int = 4 # Leaky ReLU activation
ACTIVATION_ELU: int = 5        # ELU activation
ACTIVATION_SWISH: int = 6      # Swish/SiLU activation
ACTIVATION_IDENTITY: int = 7   # Identity activation (passthrough)

# Mapping from activation function names to their indices
ACT_FN_MAP = {
    'sigmoid': ACTIVATION_SIGMOID,     # Sigmoid activation (default)
    'tanh': ACTIVATION_TANH,           # Tanh activation
    'softmax': ACTIVATION_SOFTMAX,     # Softmax activation
    'relu': ACTIVATION_RELU,           # ReLU activation
    'leaky_relu': ACTIVATION_LEAKY_RELU, # Leaky ReLU activation
    'elu': ACTIVATION_ELU,             # ELU activation
    'swish': ACTIVATION_SWISH,         # Swish/SiLU activation
    'identity': ACTIVATION_IDENTITY    # Identity activation (passthrough)
}

# ===== Visualization Defaults =====
DEFAULT_PLOT_FORMAT: str = "png"              # Default format for saving plots
DEFAULT_NETWORK_TITLE: str = "Hybrid NEAT Network"   # Default title for network visualizations
DEFAULT_EVOLUTION_TITLE: str = "Hybrid NEAT Evolution Progress"  # Default title for evolution plots