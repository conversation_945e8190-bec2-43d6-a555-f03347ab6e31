"""
Base configuration classes for NEAT algorithm.

This module defines the core configuration dataclasses used throughout the NEAT implementation.
All configurations use Flax struct.dataclass for immutability and JAX compatibility.
"""

from dataclasses import field
from typing import Optional, Any, TypeVar
from flax import struct

# Type variable for config classes
T = TypeVar('T')

# --- Population configuration ---
@struct.dataclass
class PopulationConfig:
    """Configuration for population initialization and management."""
    population_size: int = 100
    weight_init_std: float = 0.1
    weight_init_mean: float = 0.0

# --- Network configuration ---
@struct.dataclass
class NetworkConfig:
    """Configuration for network architecture and activation functions."""
    num_inputs: int = 2
    num_outputs: int = 1
    max_nodes: int = 100
    max_connections: int = 500
    activation_fn: str = "relu"
    output_activation: str = "sigmoid"
    hidden_activation: str = "relu"

# --- Species configuration ---
@struct.dataclass
class SpeciesConfig:
    """Configuration for species management and compatibility."""
    max_species: int = 10
    gene_coefficient: float = 1.0
    weight_coefficient: float = 0.5
    compatibility_threshold: float = 3.0
    stagnation_threshold: int = 15
    rank_strategy: int = 0  # 0 = linear, 1 = exponential

# --- Recombination configuration ---
@struct.dataclass
class RecombinationConfig:
    """Configuration for parent selection and crossover."""
    tournament_size: int = 3
    parent1_gene_rate: float = 0.5
    elite_ratio: float = 0.1
    cull_ratio: float = 0.1

# --- Mutation configuration ---
@struct.dataclass
class MutationConfig:
    """Configuration for structural and weight mutations."""
    add_node_rate: float = 0.03
    add_connection_rate: float = 0.05
    shift_weight_rate: float = 0.8
    weight_scale: float = 0.1
    new_weight_std: float = 1.0

# --- Fitness configuration ---
@struct.dataclass
class FitnessConfig:
    """Configuration for fitness evaluation and penalties."""
    connection_cost: float = 0.0
    node_cost: float = 0.0

# --- Backpropagation configuration ---
@struct.dataclass
class BackpropConfig:
    """Configuration for hybrid backpropagation training."""
    enabled: bool = False
    rounds: int = 10
    learning_rate: float = 0.01
    gradient_clip: float = 1.0
    max_errors: int = 100
    num_epochs: int = 1
    batch_size: int = 32
    max_weight: float = 10.0

# --- Main NEAT configuration ---
@struct.dataclass
class NEATConfig:
    """Main configuration class that combines all sub-configurations."""
    # Core evolution parameters
    seed: int = 42
    max_generations: int = 100
    target_fitness: float = float('inf')
    log_progress: bool = True
    log_frequency: int = 10
    
    # Task-specific data (set by tasks)
    inputs: Optional[Any] = None
    targets: Optional[Any] = None
    
    # Sub-configurations
    population: PopulationConfig = field(default_factory=PopulationConfig)
    network: NetworkConfig = field(default_factory=NetworkConfig)
    species: SpeciesConfig = field(default_factory=SpeciesConfig)
    recombination: RecombinationConfig = field(default_factory=RecombinationConfig)
    mutation: MutationConfig = field(default_factory=MutationConfig)
    fitness: FitnessConfig = field(default_factory=FitnessConfig)
    backprop: BackpropConfig = field(default_factory=BackpropConfig)

# --- Default configuration instance ---
DEFAULT_CONFIG = NEATConfig()
