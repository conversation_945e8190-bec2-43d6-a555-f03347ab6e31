#!/usr/bin/env python3
"""
Circle Classification Task - Hybrid NEAT Implementation

Binary classification task where points inside a circle are labeled +1
and points outside are labeled -1. This is a classic non-linearly separable
problem that tests the network's ability to learn circular decision boundaries.

Features:
- Configurable circle radius and noise levels
- Professional logging and monitoring
- Detailed performance metrics
- Real-time progress tracking
- Visualization support

Usage: python circle_task.py [--seed SEED] [--generations GENS] [--samples SAMPLES] [--noise NOISE] [--no-viz]
"""

import jax
import jax.numpy as jnp
import numpy as np
import time
import logging
import gc
import argparse
from typing import Dict, Any, Optional
from dataclasses import dataclass

# Import NEAT components
from ..config.tasks.circle import CIRCLE_CONFIG
from ..evolution import evolve_population
from ..innovation import InnovationTracker
from ..loss_fns import binary_cross_entropy
from ..population import initialize_population, convert_genomes_to_networks
from ..network import ActivationState, Network
from ..species import initialize_state
from ..visualization import create_visualization_hook, plot_fitness_history, visualize_network, plot_decision_boundary, plot_circle_boundary

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

@dataclass
class CircleDataset:
    """Container for circle classification dataset"""
    inputs: jnp.ndarray
    targets: jnp.ndarray
    radius: float
    noise: float
    num_samples: int

    def __post_init__(self):
        """Validate dataset after initialization"""
        if self.inputs.shape[0] != self.targets.shape[0]:
            raise ValueError("Inputs and targets must have same number of samples")
        if self.inputs.shape[1] != 2:
            raise ValueError("Circle task requires 2D inputs")
        if self.radius <= 0:
            raise ValueError("Radius must be positive")

def generate_circle_dataset(num_samples: int = 400, radius: float = 5.0, noise: float = 0.1, seed: int = 42) -> CircleDataset:
    """
    Generate circle classification dataset

    Args:
        num_samples: Total number of samples to generate
        radius: Radius of the circle boundary
        noise: Noise level to add to coordinates
        seed: Random seed for reproducibility

    Returns:
        CircleDataset: Generated dataset with inputs and targets
    """
    np.random.seed(seed)

    points = []
    labels = []

    # Generate points uniformly in a square region
    for _ in range(num_samples):
        # Generate point in square [-radius*1.5, radius*1.5]
        x = np.random.uniform(-radius * 1.5, radius * 1.5)
        y = np.random.uniform(-radius * 1.5, radius * 1.5)

        # Add noise
        x += np.random.uniform(-radius, radius) * noise
        y += np.random.uniform(-radius, radius) * noise

        # Calculate distance from origin
        distance = np.sqrt(x*x + y*y)

        # Label: +1 if inside circle (distance < radius), -1 if outside
        label = 1.0 if distance < radius else -1.0

        points.append([x, y])
        labels.append([label])

    inputs = jnp.array(points)
    targets = jnp.array(labels)

    logger.info(f"Generated circle dataset: {num_samples} samples, radius={radius:.1f}, noise={noise:.3f}")

    # Log class distribution
    positive_count = jnp.sum(targets == 1.0)
    negative_count = jnp.sum(targets == -1.0)
    logger.info(f"Class distribution: {positive_count} inside (+1), {negative_count} outside (-1)")

    return CircleDataset(
        inputs=inputs,
        targets=targets,
        radius=radius,
        noise=noise,
        num_samples=num_samples
    )

@dataclass
class EvolutionMetrics:
    """Container for evolution performance metrics"""
    generation: int
    current_fitness: float
    best_fitness: float
    best_generation: int
    population_diversity: float
    species_count: int
    elapsed_time: float
    breakthrough_generation: Optional[int] = None

    def __post_init__(self):
        """Validate metrics after initialization"""
        if self.generation < 0:
            raise ValueError("Generation must be non-negative")
        if self.elapsed_time < 0:
            raise ValueError("Elapsed time must be non-negative")

@dataclass
class FinalResults:
    """Container for final evolution results"""
    success: bool
    fitness: float
    accuracy: float
    generation: int
    total_time: float
    breakthrough_generation: Optional[int]
    test_outputs: Dict[str, Any]
    configuration_name: str
    dataset_info: Dict[str, Any]

# Global variables for the current dataset (will be set before evolution)
CURRENT_CIRCLE_INPUTS = None
CURRENT_CIRCLE_TARGETS = None

def evaluate_circle_fitness(*args) -> float:
    """
    Circle classification fitness function with robust sigmoid scaling

    Args:
        *args: Variable arguments supporting multiple calling conventions

    Returns:
        float: Fitness value (higher is better, range 0-10)
    """
    try:
        if len(args) == 2:
            predictions, targets = args
            # Use binary cross-entropy loss, convert to fitness with sigmoid scaling
            loss = binary_cross_entropy(predictions, targets)
            return 10.0 / (1.0 + loss)  # Robust sigmoid scaling: always positive, bounded 0-10
        elif len(args) == 3:
            network, activation_state, _ = args
            outputs, _ = network.forward(CURRENT_CIRCLE_INPUTS, activation_state)
            loss = binary_cross_entropy(outputs, CURRENT_CIRCLE_TARGETS)
            return 10.0 / (1.0 + loss)  # Same robust scaling
        else:
            raise ValueError(f"Invalid number of arguments: {len(args)}. Expected 2 or 3.")
    except Exception as e:
        logger.error(f"Error in fitness evaluation: {e}")
        return 0.0  # Return 0 instead of -inf for better stability

def calculate_circle_accuracy(network: Network, activation_state: ActivationState, dataset: CircleDataset) -> float:
    """
    Calculate circle classification accuracy

    Args:
        network: Neural network to test
        activation_state: Network activation state
        dataset: Circle dataset to test on

    Returns:
        float: Accuracy percentage (0-100)
    """
    try:
        outputs, _ = network.forward(dataset.inputs, activation_state)

        # Network outputs are already sigmoid-activated probabilities, no need for additional sigmoid
        probabilities = outputs  # Already probabilities from network's sigmoid output activation
        predictions = jnp.where(probabilities > 0.5, 1.0, -1.0)

        # Calculate accuracy
        correct = jnp.sum(predictions.flatten() == dataset.targets.flatten())
        accuracy = (correct / len(dataset.targets)) * 100.0

        return float(accuracy)
    except Exception as e:
        logger.error(f"Error calculating accuracy: {e}")
        return 0.0

def get_population_diversity(fitnesses: jnp.ndarray) -> float:
    """Calculate population diversity metric"""
    try:
        return float(jnp.std(fitnesses))
    except Exception:
        return 0.0

def create_circle_config(max_generations: int = 20, dataset: CircleDataset = None) -> Any:
    """
    Create configuration optimized for circle classification

    Args:
        max_generations: Maximum number of generations (default: 20)
        dataset: Circle dataset (for inputs and targets)
    """
    return CIRCLE_CONFIG.replace(
        inputs=dataset.inputs if dataset else None,
        targets=dataset.targets if dataset else None,
        max_generations=max_generations
    )

def log_generation_progress(metrics: EvolutionMetrics, config: Any) -> None:
    """Log detailed generation progress with production-quality formatting"""

    # Determine status emoji and message
    if metrics.best_fitness >= 9.5:
        status = "🎉 EXCELLENT"
        color = "\033[92m"  # Green
    elif metrics.best_fitness >= 8.5:
        status = "✨ VERY GOOD"
        color = "\033[93m"  # Yellow
    elif metrics.best_fitness >= 7.0:
        status = "🔥 GOOD"
        color = "\033[96m"  # Cyan
    else:
        status = "🔄 EVOLVING"
        color = "\033[90m"  # Gray

    reset_color = "\033[0m"

    # Log main progress
    logger.info(
        f"{color}Gen {metrics.generation:2d}: "
        f"Current={metrics.current_fitness:.4f}, "
        f"Best={metrics.best_fitness:.4f} (Gen {metrics.best_generation}), "
        f"Species={metrics.species_count}, "
        f"Diversity={metrics.population_diversity:.3f}, "
        f"Time={metrics.elapsed_time:.1f}s - {status}{reset_color}"
    )

    # Log breakthrough moments
    if metrics.breakthrough_generation == metrics.generation:
        logger.info(f"🚀 BREAKTHROUGH at generation {metrics.generation}! Fitness: {metrics.best_fitness:.4f}")

def run_circle_evolution(config: Any, dataset: CircleDataset, seed: int = 42, enable_viz: bool = True, 
                        configuration_name: str = "Circle Classification") -> FinalResults:
    """
    Run circle classification evolution with comprehensive monitoring

    Args:
        config: NEAT configuration
        dataset: Circle dataset to train on
        seed: Random seed for reproducibility
        enable_viz: Whether to enable visualizations (default: True)
        configuration_name: Name of the configuration

    Returns:
        FinalResults: Comprehensive results object
    """
    # Set global dataset variables for fitness function
    global CURRENT_CIRCLE_INPUTS, CURRENT_CIRCLE_TARGETS
    CURRENT_CIRCLE_INPUTS = dataset.inputs
    CURRENT_CIRCLE_TARGETS = dataset.targets

    logger.info("🚀 Starting Circle Classification Evolution")
    logger.info(f"Dataset: {dataset.num_samples} samples, radius={dataset.radius:.1f}, noise={dataset.noise:.3f}")
    logger.info(f"Population: {config.population.population_size}")
    logger.info(f"Max Generations: {config.max_generations}")
    logger.info(f"Backprop: {config.backprop.rounds} rounds @ LR {config.backprop.learning_rate}")
    logger.info(f"Random Seed: {seed}")
    logger.info(f"JAX Backend: {jax.default_backend()}")
    logger.info("-" * 60)

    start_time = time.time()

    # Initialize evolution components
    key = jax.random.PRNGKey(seed)
    tracker = InnovationTracker.create(config.network)

    key, init_key = jax.random.split(key)
    population, tracker = initialize_population(
        key=init_key,
        innovation_tracker=tracker,
        config=config
    )

    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=config
    )

    key, species_key = jax.random.split(key)
    species_state = initialize_state(
        connections=population.connections,
        key=species_key,
        config=config.species
    )

    # Create results directory and setup visualization
    import os
    results_dir = "log/circle"
    viz_hook = None

    if enable_viz:
        os.makedirs(results_dir, exist_ok=True)

        # Create visualization hook
        viz_hook = create_visualization_hook(
            output_dir=results_dir,
            plot_interval=10,     # Plot every 10 generations
            network_interval=15,  # Network visualization every 15 generations
            plot_species=True,    # Include species count
            max_networks_per_gen=1,  # Just the best network
            show_weights=True,    # Show connection weights
            format='png'          # PNG format
        )

    # Evolution tracking
    best_fitness = -jnp.inf
    best_network_data = None
    best_generation = -1
    breakthrough_generation = None
    fitness_history = []
    species_history = []

    logger.info("Evolution started...")
    if enable_viz:
        logger.info(f"📊 Visualizations will be saved to: {results_dir}")
    else:
        logger.info("📊 Visualizations disabled for faster execution")

    # Use the global fitness function
    fitness_fn = evaluate_circle_fitness

    # Main evolution loop
    for generation in range(config.max_generations):
        gen_start_time = time.time()

        # Evolve population
        networks, species_state, tracker, fitnesses, key = evolve_population(
            networks=networks,
            species_state=species_state,
            tracker=tracker,
            key=key,
            fitness_fn=fitness_fn,
            inputs=dataset.inputs,
            config=config
        )

        # Calculate metrics
        current_best_idx = jnp.argmax(fitnesses)
        current_best_fitness = float(fitnesses[current_best_idx])
        population_diversity = get_population_diversity(fitnesses)
        species_count = len(jnp.unique(species_state.species_ids))
        gen_elapsed_time = time.time() - gen_start_time

        # Check for improvement
        if current_best_fitness > best_fitness:
            best_fitness = current_best_fitness
            best_generation = generation

            # Store best network data
            best_network_data = {
                'connections': networks.connections[current_best_idx].copy(),
                'enabled': networks.enabled[current_best_idx].copy(),
                'node_types': networks.node_types[current_best_idx].copy(),
                'activation_fns': networks.activation_fns[current_best_idx].copy(),
            }

            # Check for breakthrough (first time > 8.0)
            if best_fitness > 8.0 and breakthrough_generation is None:
                breakthrough_generation = generation

        # Create metrics object
        metrics = EvolutionMetrics(
            generation=generation,
            current_fitness=current_best_fitness,
            best_fitness=best_fitness,
            best_generation=best_generation,
            population_diversity=population_diversity,
            species_count=species_count,
            elapsed_time=gen_elapsed_time,
            breakthrough_generation=breakthrough_generation if breakthrough_generation == generation else None
        )

        # Update history for visualization
        fitness_history.append(current_best_fitness)
        species_history.append(species_count)

        # Call visualization hook
        if enable_viz and viz_hook is not None:
            try:
                viz_hook(
                    generation=generation,
                    networks=networks,
                    species_state=species_state,
                    fitnesses=fitnesses,
                    best_fitness=current_best_fitness,
                    best_idx=current_best_idx
                )
            except Exception as e:
                logger.warning(f"⚠️  Visualization error: {e}")

        # Log progress
        log_generation_progress(metrics, config)

        # Early termination for excellent solution
        if best_fitness >= 9.5:
            logger.info(f"🎉 EXCELLENT SOLUTION ACHIEVED! Terminating early at generation {generation}")
            break

    total_time = time.time() - start_time

    # Generate final visualization summary
    if enable_viz:
        try:
            # Final fitness history plot
            plot_fitness_history(
                fitness_history=jnp.array(fitness_history),
                species_counts=jnp.array(species_history),
                title=f"Circle Evolution: Classification (Seed {seed})",
                save_path=f"{results_dir}/final_fitness_history.png",
                show=False
            )

            if len(fitness_history) > 0:
                logger.info(f"📊 Generated {len(fitness_history)} generation plots + final summary")
                logger.info(f"🎨 Visualization files saved to: {results_dir}")
        except Exception as e:
            logger.warning(f"⚠️  Final visualization error: {e}")

    # Reconstruct best network and evaluate
    if best_network_data is None:
        logger.error("❌ No valid network found during evolution!")
        return FinalResults(
            success=False, fitness=0.0, accuracy=0.0, generation=-1,
            total_time=total_time, breakthrough_generation=None,
            test_outputs={}, configuration_name=configuration_name,
            dataset_info={"samples": dataset.num_samples, "radius": dataset.radius, "noise": dataset.noise}
        )

    # Create best network
    best_network = Network(
        connections=best_network_data['connections'],
        enabled=best_network_data['enabled'],
        node_types=best_network_data['node_types'],
        activation_fns=best_network_data['activation_fns'],
        num_inputs=config.network.num_inputs,
        num_outputs=config.network.num_outputs,
        max_nodes=config.network.max_nodes
    )

    activation_state = ActivationState(
        node_depths=jnp.full(config.network.max_nodes, -1, dtype=jnp.int32),
        outdated_depths=True
    )

    # Visualize the best network
    if enable_viz:
        try:
            visualize_network(
                network=best_network,
                title=f"Best Circle Network (Gen {best_generation}, Fitness {best_fitness:.4f})",
                save_path=f"{results_dir}/best_network_final",
                format='png'
            )
            logger.info(f"🎨 Best network visualization saved")
        except Exception as e:
            logger.warning(f"⚠️  Best network visualization error: {e}")

        # Visualize decision boundary
        try:
            plot_decision_boundary(
                network=best_network,
                dataset_inputs=dataset.inputs,
                dataset_targets=dataset.targets,
                title=f"Circle Decision Boundary (Gen {best_generation}, Fitness {best_fitness:.4f})",
                true_boundary_fn=plot_circle_boundary,
                boundary_params={'radius': dataset.radius, 'center': (0.0, 0.0)},
                save_path=f"{results_dir}/decision_boundary.png",
                show=False,
                resolution=200
            )
            logger.info(f"🎨 Decision boundary visualization saved")
        except Exception as e:
            logger.warning(f"⚠️  Decision boundary visualization error: {e}")

    # Calculate final accuracy
    accuracy = calculate_circle_accuracy(best_network, activation_state, dataset)

    # Test on sample points for detailed analysis
    test_outputs = {}
    sample_indices = [0, len(dataset.inputs)//4, len(dataset.inputs)//2, 3*len(dataset.inputs)//4, len(dataset.inputs)-1]

    for i, idx in enumerate(sample_indices):
        if idx >= len(dataset.inputs) or idx < 0:
            continue
        try:
            input_point = dataset.inputs[idx:idx+1]
            expected = dataset.targets[idx][0]
            output, _ = best_network.forward(input_point, activation_state)

            # Check if output is valid
            if output.size == 0:
                logger.warning(f"Empty output for sample {idx}, skipping")
                continue

            actual = float(output[0, 0])
            probability = float(output[0, 0])  # Already sigmoid-activated, no need for double sigmoid
            predicted_class = 1.0 if probability > 0.5 else -1.0
            correct = predicted_class == expected

            test_outputs[f"Sample_{i+1}"] = {
                'input': [float(input_point[0, 0]), float(input_point[0, 1])],
                'expected': float(expected),
                'raw_output': actual,
                'probability': probability,
                'predicted_class': float(predicted_class),
                'correct': correct
            }
        except Exception as e:
            logger.warning(f"Error processing sample {idx}: {e}")
            continue

    return FinalResults(
        success=best_fitness >= 3.0,  # Adjusted for sigmoid scaling (3.0 = good performance)
        fitness=best_fitness,
        accuracy=accuracy,
        generation=best_generation,
        total_time=total_time,
        breakthrough_generation=breakthrough_generation,
        test_outputs=test_outputs,
        configuration_name=configuration_name,
        dataset_info={"samples": dataset.num_samples, "radius": dataset.radius, "noise": dataset.noise}
    )

def print_final_results(results: FinalResults) -> None:
    """Print comprehensive final results with production formatting"""

    print("\n" + "=" * 80)
    print("🎯 CIRCLE CLASSIFICATION EVOLUTION RESULTS")
    print("=" * 80)

    # Success status
    if results.success:
        status_emoji = "🎉"
        status_text = "SUCCESS - EXCELLENT SOLUTION"
        status_color = "\033[92m"  # Green
    elif results.accuracy >= 80:
        status_emoji = "✅"
        status_text = "GOOD RESULT"
        status_color = "\033[93m"  # Yellow
    else:
        status_emoji = "⚠️"
        status_text = "SUBOPTIMAL RESULT"
        status_color = "\033[91m"  # Red

    reset_color = "\033[0m"

    print(f"{status_color}{status_emoji} {status_text}{reset_color}")
    print()

    # Core metrics
    print(f"📊 Performance Metrics:")
    print(f"   Fitness Score:      {results.fitness:.6f} / 10.000000")
    print(f"   Classification:     {results.accuracy:.1f}% accuracy")
    print(f"   Convergence:        Generation {results.generation}")
    print(f"   Total Runtime:      {results.total_time:.2f} seconds")

    if results.breakthrough_generation is not None:
        print(f"   Breakthrough:       Generation {results.breakthrough_generation}")

    print(f"   Configuration:      {results.configuration_name}")
    print()

    # Dataset info
    print(f"🔍 Dataset Information:")
    print(f"   Samples:            {results.dataset_info['samples']}")
    print(f"   Circle Radius:      {results.dataset_info['radius']:.1f}")
    print(f"   Noise Level:        {results.dataset_info['noise']:.3f}")
    print()

    # Sample test cases
    print(f"🔍 Sample Test Cases:")
    print(f"   {'Sample':<10} {'Input (x,y)':<15} {'Expected':<10} {'Predicted':<10} {'Prob':<8} {'Status'}")
    print(f"   {'-'*10} {'-'*15} {'-'*10} {'-'*10} {'-'*8} {'-'*6}")

    for sample_name, sample_data in results.test_outputs.items():
        status = "✓ PASS" if sample_data['correct'] else "✗ FAIL"
        status_color = "\033[92m" if sample_data['correct'] else "\033[91m"

        input_str = f"({sample_data['input'][0]:+.2f},{sample_data['input'][1]:+.2f})"

        print(f"   {sample_name:<10} {input_str:<15} {sample_data['expected']:>+8.1f}  "
              f"{sample_data['predicted_class']:>+8.1f}  {sample_data['probability']:>6.3f}  "
              f"{status_color}{status}{reset_color}")

    print()

    # Performance analysis
    print(f"⚡ Performance Analysis:")
    efficiency_rating = "EXCELLENT" if results.generation <= 15 else "GOOD" if results.generation <= 30 else "MODERATE"
    print(f"   Convergence Speed:  {efficiency_rating} ({results.generation} generations)")
    print(f"   Solution Quality:   {'EXCELLENT' if results.fitness >= 9.0 else 'GOOD' if results.fitness >= 8.0 else 'MODERATE'}")
    print(f"   Runtime Efficiency: {results.total_time/max(results.generation, 1):.2f}s per generation")

    if results.success:
        print(f"\n🚀 Circle classification demonstrates NEAT's ability to learn non-linear boundaries!")
        print(f"   The network successfully learned to distinguish inside vs outside the circle.")

def cleanup_resources() -> None:
    """Clean up computational resources"""
    try:
        jax.clear_caches()
        gc.collect()
        logger.info("🧹 Resources cleaned up successfully")
    except Exception as e:
        logger.warning(f"⚠️ Resource cleanup warning: {e}")

def main():
    """Main execution function with argument parsing"""
    parser = argparse.ArgumentParser(description="Circle Classification with Hybrid NEAT")
    parser.add_argument('--seed', type=int, default=42, help='Random seed (default: 42)')
    parser.add_argument('--generations', type=int, help='Max generations (default: from config)')
    parser.add_argument('--samples', type=int, default=100, help='Number of dataset samples (default: 100)')
    parser.add_argument('--radius', type=float, default=5.0, help='Circle radius (default: 5.0)')
    parser.add_argument('--noise', type=float, default=0.1, help='Noise level (default: 0.1)')
    parser.add_argument('--no-viz', action='store_true', help='Disable visualizations for faster execution')
    parser.add_argument('--verbose', action='store_true', help='Enable verbose logging')

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Generate dataset
        dataset = generate_circle_dataset(
            num_samples=args.samples,
            radius=args.radius,
            noise=args.noise,
            seed=args.seed
        )

        # Create configuration
        config = create_circle_config(
            max_generations=args.generations if args.generations is not None else CIRCLE_CONFIG.max_generations,
            dataset=dataset
        )

        # Log configuration
        config_name = "Circle Classification"
        logger.info(f"🔧 Using {config_name} configuration")
        logger.info("⚡ Standard config with multiple species support!")
        logger.info("   Population: 200, Max nodes: 75, Backprop: 12 rounds, Connection cost: 0.006, Node cost: 0.012")

        # Run evolution (enable_viz is opposite of no_viz)
        results = run_circle_evolution(config, dataset, args.seed, enable_viz=not args.no_viz, configuration_name=config_name)

        # Display results
        print_final_results(results)

        # Return appropriate exit code
        return 0 if results.success else 1

    except Exception as e:
        logger.error(f"❌ Evolution failed: {e}")
        raise
    finally:
        cleanup_resources()

if __name__ == "__main__":
    exit(main())