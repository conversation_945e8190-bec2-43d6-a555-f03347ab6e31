
es_name: "ARS_native"
problem_type: "cartpole_hard"
normalize: false
es_config:
  pop_size: 100
  elite_ratio: 0.1
  init_stdev: 0.1
  decay_stdev: 0.999
  limit_stdev: 0.01
  optimizer: "sgd"
  optimizer_config:
    lrate_init: 0.075
    lrate_decay_steps: 1
    lrate_limit: 0.001
    decay_coef: 1
hidden_size: 64
hidden_size: 64
num_tests: 100
n_repeats: 16
max_iter: 2000
test_interval: 100
log_interval: 20
seed: 42
gpu_id: 0
debug: false