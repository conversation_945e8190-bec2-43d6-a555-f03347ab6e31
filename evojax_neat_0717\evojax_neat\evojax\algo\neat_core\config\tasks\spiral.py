"""
Spiral classification task-specific configuration for the NEAT algorithm.

This module provides optimized configurations for the spiral classification problem,
where points from two interleaved spirals are labeled +1 and -1. This is one of
the most challenging 2D classification problems requiring complex decision boundaries.
"""
from ..base import (
    PopulationConfig,
    NetworkConfig,
    SpeciesConfig,
    RecombinationConfig,
    MutationConfig,
    FitnessConfig,
    BackpropConfig,
    NEATConfig
)

# --- Spiral-specific configurations ---

# Network configuration for spiral classification
SPIRAL_NETWORK_CONFIG = NetworkConfig(
    num_inputs=2,  # Spiral task has 2D inputs (x, y)
    num_outputs=1,  # Binary classification
    max_nodes=50,   # More nodes needed for complex spiral boundaries
    max_connections=200,  # More connections for complexity
    activation_fn="tanh",
    output_activation="sigmoid",  # Sigmoid for binary classification
    hidden_activation="tanh"
)

# Population configuration for spiral
SPIRAL_POPULATION_CONFIG = PopulationConfig(
    population_size=300,  # Larger population for harder problem
    weight_init_std=2.5,  # Moderate initial weights
    weight_init_mean=0.0
)

# Species configuration for spiral - Ultra-strict weight-based speciation
SPIRAL_SPECIES_CONFIG = SpeciesConfig(
    max_species=20,              # More species for diversity
    gene_coefficient=1.0,        # Standard structural penalty
    weight_coefficient=1.0,      # HIGH weight sensitivity for identical topologies
    compatibility_threshold=0.01, # ULTRA-STRICT threshold for weight-based speciation
    stagnation_threshold=12,     # Allow more time for complex solutions
    rank_strategy=1  # Exponential ranking
)

# Recombination configuration for spiral
SPIRAL_RECOMBINATION_CONFIG = RecombinationConfig(
    tournament_size=3,
    parent1_gene_rate=0.5,
    elite_ratio=0.03,  # Keep some elites
    cull_ratio=0.75    # Aggressive selection pressure
)

# Mutation configuration for spiral
SPIRAL_MUTATION_CONFIG = MutationConfig(
    add_node_rate=0.25,     # Higher structural mutation for complexity
    add_connection_rate=0.6, # Higher connection mutation
    shift_weight_rate=0.9,   # Very high weight mutation
    weight_scale=4.0         # Larger weight changes
)

# Fitness configuration for spiral
SPIRAL_FITNESS_CONFIG = FitnessConfig(
    connection_cost=0.0005,  # Very small penalty for complexity
    node_cost=0.0005
)

# Backpropagation configuration for spiral
SPIRAL_BACKPROP_CONFIG = BackpropConfig(
    enabled=True,
    rounds=25,           # More backprop rounds
    learning_rate=0.06,  # Moderate learning rate
    gradient_clip=3.0,   # Higher gradient clipping for complex problem
    max_errors=100,      # Higher error tolerance for complex problem
    num_epochs=6,        # More epochs
    batch_size=32,       # Reasonable batch size
    max_weight=20.0      # Allow larger weights
)

# Enhanced backprop config for complex spiral learning
SPIRAL_ENHANCED_BACKPROP_CONFIG = BackpropConfig(
    enabled=True,
    rounds=15,           # More aggressive backprop
    learning_rate=0.08,  # Higher learning rate
    gradient_clip=2.5,   # Moderate gradient clipping
    max_errors=75,       # Moderate error tolerance
    num_epochs=4,        # Moderate epochs
    batch_size=24,       # Smaller batches for precision
    max_weight=15.0      # Moderate max weight
)

# Enhanced fitness config for spiral - encourage complexity
SPIRAL_ENHANCED_FITNESS_CONFIG = FitnessConfig(
    connection_cost=0.003,  # Small penalty - we want complexity for spirals
    node_cost=0.005         # Small node penalty
)

# Main spiral configuration (Simple)
SPIRAL_SIMPLE_CONFIG = NEATConfig(
    # Core evolution parameters
    seed=42,
    max_generations=100,  # More generations for harder problem
    target_fitness=8.5,   # Lower threshold for spiral (harder problem)
    log_progress=True,
    log_frequency=5,      # Log every 5 generations

    # Sub-configurations
    population=SPIRAL_POPULATION_CONFIG,
    network=SPIRAL_NETWORK_CONFIG,
    species=SPIRAL_SPECIES_CONFIG,
    recombination=SPIRAL_RECOMBINATION_CONFIG,
    mutation=SPIRAL_MUTATION_CONFIG,
    fitness=SPIRAL_FITNESS_CONFIG,
    backprop=SPIRAL_BACKPROP_CONFIG
)

# Fast Ultra configuration for spiral - optimized based on circle task success
SPIRAL_FAST_ULTRA_CONFIG = NEATConfig(
    # Core evolution parameters
    seed=42,
    max_generations=30,   # Fewer generations based on circle success
    target_fitness=8.0,   # Adjusted for spiral complexity
    log_progress=True,
    log_frequency=2,      # More frequent logging

    # Fast ultra sub-configurations
    population=PopulationConfig(
        population_size=250,  # Larger population for spiral complexity
        weight_init_std=3.0,  # Higher initial weight variance
        weight_init_mean=0.0
    ),
    network=NetworkConfig(
        num_inputs=2,
        num_outputs=1,
        max_nodes=100,        # High complexity for spiral boundaries
        max_connections=400,  # High connections for spiral complexity
        activation_fn="tanh",
        output_activation="sigmoid",
        hidden_activation="tanh"
    ),
    species=SpeciesConfig(
        max_species=25,              # Good species diversity
        gene_coefficient=1.0,        # Standard structural penalty
        weight_coefficient=1.0,      # HIGH weight sensitivity for identical topologies
        compatibility_threshold=0.01, # ULTRA-STRICT threshold for weight-based speciation
        stagnation_threshold=20,     # Allow longer stagnation for complex problem
        rank_strategy=1
    ),
    recombination=SPIRAL_RECOMBINATION_CONFIG,
    mutation=MutationConfig(
        add_node_rate=0.6,      # Very high structural mutation for spiral complexity
        add_connection_rate=0.8, # Very high connection rate
        shift_weight_rate=0.95,  # Almost always mutate weights
        weight_scale=4.5         # Large weight changes for spiral boundaries
    ),
    fitness=FitnessConfig(
        connection_cost=0.004,  # Small penalty - spirals need complexity
        node_cost=0.008         # Small node penalty
    ),
    backprop=BackpropConfig(
        enabled=True,
        rounds=15,           # More backprop rounds for spiral complexity
        learning_rate=0.08,  # Higher learning rate
        gradient_clip=2.5,   # Moderate gradient clipping
        max_errors=80,       # Higher error tolerance for complex problem
        num_epochs=4,        # Moderate epochs
        batch_size=20,       # Smaller batches for precision
        max_weight=18.0      # Higher max weight for spiral complexity
    )
)

# Ultra-Aggressive configuration for maximum spiral complexity
SPIRAL_ULTRA_AGGRESSIVE_CONFIG = NEATConfig(
    # Core evolution parameters
    seed=42,
    max_generations=50,   # More generations for ultra complexity
    target_fitness=8.5,   # High target for spiral
    log_progress=True,
    log_frequency=3,

    # Ultra-aggressive sub-configurations
    population=PopulationConfig(
        population_size=350,  # Very large population for spiral diversity
        weight_init_std=3.5,  # Very high initial weight variance
        weight_init_mean=0.0
    ),
    network=NetworkConfig(
        num_inputs=2,
        num_outputs=1,
        max_nodes=150,        # Maximum complexity for spiral boundaries
        max_connections=600,  # Maximum connections for spiral complexity
        activation_fn="tanh",
        output_activation="sigmoid",
        hidden_activation="tanh"
    ),
    species=SpeciesConfig(
        max_species=30,              # Maximum species diversity
        gene_coefficient=1.0,        # Standard structural penalty
        weight_coefficient=1.0,      # HIGH weight sensitivity for identical topologies
        compatibility_threshold=0.02, # ULTRA-STRICT threshold for weight-based speciation
        stagnation_threshold=25,     # Allow long stagnation for complex solutions
        rank_strategy=1
    ),
    recombination=SPIRAL_RECOMBINATION_CONFIG,
    mutation=MutationConfig(
        add_node_rate=0.7,      # Maximum structural mutation
        add_connection_rate=0.9, # Maximum connection rate
        shift_weight_rate=0.98,  # Almost always mutate weights
        weight_scale=5.0         # Maximum weight changes
    ),
    fitness=FitnessConfig(
        connection_cost=0.002,  # Very small penalty - maximum complexity allowed
        node_cost=0.004         # Very small node penalty
    ),
    backprop=BackpropConfig(
        enabled=True,
        rounds=20,           # Maximum backprop rounds
        learning_rate=0.09,  # High learning rate
        gradient_clip=3.0,   # Higher gradient clipping
        max_errors=100,      # Maximum error tolerance
        num_epochs=5,        # More epochs
        batch_size=16,       # Smaller batches for maximum precision
        max_weight=25.0      # Maximum weight for complex spiral boundaries
    )
)