{"cells": [{"cell_type": "code", "execution_count": 1, "id": "86d33650", "metadata": {}, "outputs": [], "source": ["# make high resolution animation from svg\n", "import os\n", "import glob\n", "import cairosvg\n", "import IPython\n", "from PIL import Image\n", "import numpy as np"]}, {"cell_type": "code", "execution_count": 2, "id": "3615c714", "metadata": {}, "outputs": [], "source": ["def pure_pil_alpha_to_color_v2(image, color=(255, 255, 255)):\n", "    \"\"\"Alpha composite an RGBA Image with a specified color.\n", "\n", "    Simpler, faster version than the solutions above.\n", "\n", "    Source: http://stackoverflow.com/a/9459208/284318\n", "\n", "    Keyword Arguments:\n", "    image -- PIL RGBA Image object\n", "    color -- Tuple r, g, b (default 255, 255, 255)\n", "\n", "    \"\"\"\n", "    image.load()  # needed for split()\n", "    background = Image.new('RGB', image.size, color)\n", "    background.paste(image, mask=image.split()[3])  # 3 is the alpha channel\n", "    return background"]}, {"cell_type": "code", "execution_count": 3, "id": "a35d13d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["canvas_record.000000.svg\n", "canvas_record.000001.svg\n", "canvas_record.000002.svg\n", "canvas_record.000003.svg\n", "canvas_record.000004.svg\n", "canvas_record.000005.svg\n", "canvas_record.000006.svg\n", "canvas_record.000007.svg\n", "canvas_record.000008.svg\n", "canvas_record.000009.svg\n", "canvas_record.000010.svg\n", "canvas_record.000011.svg\n", "canvas_record.000012.svg\n", "canvas_record.000013.svg\n", "canvas_record.000014.svg\n", "canvas_record.000015.svg\n", "canvas_record.000016.svg\n", "canvas_record.000017.svg\n", "canvas_record.000018.svg\n", "canvas_record.000019.svg\n", "canvas_record.000020.svg\n", "canvas_record.000021.svg\n", "canvas_record.000022.svg\n", "canvas_record.000023.svg\n", "canvas_record.000024.svg\n", "canvas_record.000025.svg\n", "canvas_record.000026.svg\n", "canvas_record.000027.svg\n", "canvas_record.000028.svg\n", "canvas_record.000029.svg\n", "canvas_record.000030.svg\n", "canvas_record.000031.svg\n", "canvas_record.000032.svg\n", "canvas_record.000033.svg\n", "canvas_record.000034.svg\n", "canvas_record.000035.svg\n", "canvas_record.000036.svg\n", "canvas_record.000037.svg\n", "canvas_record.000038.svg\n", "canvas_record.000039.svg\n", "canvas_record.000040.svg\n", "canvas_record.000041.svg\n", "canvas_record.000042.svg\n", "canvas_record.000043.svg\n", "canvas_record.000044.svg\n", "canvas_record.000045.svg\n", "canvas_record.000046.svg\n", "canvas_record.000047.svg\n", "canvas_record.000048.svg\n", "canvas_record.000049.svg\n", "canvas_record.000050.svg\n", "canvas_record.000051.svg\n", "canvas_record.000052.svg\n", "canvas_record.000053.svg\n", "canvas_record.000054.svg\n", "canvas_record.000055.svg\n", "canvas_record.000056.svg\n", "canvas_record.000057.svg\n", "canvas_record.000058.svg\n", "canvas_record.000059.svg\n", "canvas_record.000060.svg\n"]}], "source": ["frames = []\n", "imgs = glob.glob(\"canvas_record.*.svg\")\n", "imgs.sort()\n", "for file in imgs:\n", "    print(file)\n", "    cairosvg.svg2png(url=file, write_to=file+\".png\", scale=4)"]}, {"cell_type": "code", "execution_count": 4, "id": "80b0586d", "metadata": {}, "outputs": [], "source": ["# make a gif\n", "frames = []\n", "imgs = glob.glob(\"canvas_record.*.svg.png\")\n", "imgs.sort()\n", "for file in imgs:\n", "    new_frame = Image.open(file)\n", "    frames.append(pure_pil_alpha_to_color_v2(new_frame))\n", "frames[0].save('hires.gif', save_all=True, append_images=frames[1:], optimize=True, duration=200, loop=0)"]}, {"cell_type": "code", "execution_count": 6, "id": "363121b6", "metadata": {}, "outputs": [], "source": ["#IPython.display.Image('hires.gif', format='png')"]}, {"cell_type": "code", "execution_count": null, "id": "b7c19bd0", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.10"}}, "nbformat": 4, "nbformat_minor": 5}