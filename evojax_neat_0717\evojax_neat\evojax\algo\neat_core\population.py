import jax
import jax.numpy as jnp
from flax import struct

from .innovation import InnovationTracker
from .network import Network, ActivationState, NetworkBatch, init_network
from .config.base import NEATConfig, PopulationConfig
from .constants import (
    NODE_BIAS,
    NODE_INPUT,
    NODE_OUTPUT,
    NODE_HIDDEN,
    NODE_UNUSED,
    EMPTY_SLOT,
    ACT_FN_MAP,
    ACTIVATION_IDENTITY
)

@struct.dataclass
class Population:
    """NEAT population container with genomic operations.

    Attributes:
        connections: Array of shape [population_size, max_connections, 4]
                   Each connection is (sender, receiver, weight, innovation_id)
        enabled: Boolean array of shape [population_size, max_connections]
                indicating which connections are active
        num_connections: Array of shape [population_size] tracking the number
                       of active connections per genome
    """
    connections: jnp.ndarray  # shape=(population_size, max_connections, 4), dtype=float32
    enabled: jnp.ndarray  # shape=(population_size, max_connections), dtype=bool
    num_connections: jnp.ndarray  # shape=(population_size,), dtype=int32

    def get_genome_data(
        self,
        genome_idx: jnp.ndar<PERSON>
    ) -> tuple[jnp.ndarray, jnp.ndarray, jnp.ndarray]:
        """Extract connection data for a specific genome.

        Args:
            genome_idx: Index of genome to retrieve (int or array)

        Returns:
            Tuple of (connections, enabled_mask, connection_count) for the specified genome:
                connections: Array of shape [max_connections, 4] containing connection data
                enabled_mask: Boolean array of shape [max_connections] indicating active connections
                connection_count: Scalar indicating number of active connections
        """
        return (
            self.connections[genome_idx],
            self.enabled[genome_idx],
            self.num_connections[genome_idx]
        )

    def update_genome(
        self,
        genome_idx: jnp.ndarray,
        connections: jnp.ndarray,
        enabled: jnp.ndarray,
        num_connections: jnp.ndarray
    ) -> 'Population':
        """Update a genome with new connection data.

        Args:
            genome_idx: Index of genome to update
            connections: New connection array for genome [max_connections, 4]
            enabled: New enabled mask for genome [max_connections]
            num_connections: New connection count (scalar)

        Returns:
            Updated Population instance with the specified genome modified
        """
        return self.replace(
            connections=self.connections.at[genome_idx].set(connections),
            enabled=self.enabled.at[genome_idx].set(enabled),
            num_connections=self.num_connections.at[genome_idx].set(num_connections)
        )

    def create_network(
        self,
        genome_idx: int,
        config: NEATConfig,
    ) -> tuple[Network, ActivationState]:
        """Create executable network from a genome in the population.

        Converts the genome's connection data into a Network object that can be
        used for forward propagation.

        Args:
            genome_idx: Index of genome to materialize into a network (default: 0)
            config: NEATConfig instance containing network configuration

        Returns:
            Tuple of (Network instance, fresh ActivationState):
                Network: The network object created from the genome
                ActivationState: Initial activation state for the network
        """
        # Get parameters from config
        max_nodes = config.network.max_nodes

        # Extract the genome data
        connections, enabled, _ = self.get_genome_data(genome_idx)

        # Initialize the network from the genome data
        network = init_network(
            connections=connections,
            enabled=enabled,
            config=config.network
        )

        # Create a fresh activation state for the network
        activation_state = ActivationState(
            node_depths=jnp.full(max_nodes, -1, dtype=jnp.int32)
        )

        return network, activation_state

def initialize_population(
    key: jax.Array,
    innovation_tracker: InnovationTracker,
    config: NEATConfig
) -> tuple[Population, InnovationTracker]:
    """Initialize a new population of genomes."""
    # Extract parameters from config
    pop_size = config.population.population_size
    num_inputs = config.network.num_inputs
    num_outputs = config.network.num_outputs
    max_connections = config.network.max_connections
    weight_init_std = config.population.weight_init_std
    weight_init_mean = config.population.weight_init_mean

    # Create connection matrix for each genome
    # Each input connects to each output, plus bias connections to outputs
    total_connections = (num_inputs * num_outputs) + num_outputs  # Input->output + bias->output

    # Create arrays for all genomes (use float32 for connections to accommodate weights)
    connections = jnp.full((pop_size, max_connections, 4), float(EMPTY_SLOT), dtype=jnp.float32)
    enabled = jnp.zeros((pop_size, max_connections), dtype=bool)
    num_connections = jnp.full(pop_size, total_connections, dtype=jnp.int32)

    # Create connection indices
    # First create bias->output connections
    bias_senders = jnp.zeros(num_outputs, dtype=jnp.int32)
    bias_receivers = jnp.arange(num_outputs, dtype=jnp.int32) + num_inputs + 1  # +1 for bias node

    # Then create input->output connections
    input_senders = jnp.repeat(jnp.arange(1, num_inputs + 1, dtype=jnp.int32), num_outputs)
    output_receivers = jnp.tile(jnp.arange(num_inputs + 1, num_inputs + num_outputs + 1, dtype=jnp.int32), num_inputs)

    # Combine all connections
    senders = jnp.concatenate([bias_senders, input_senders])
    receivers = jnp.concatenate([bias_receivers, output_receivers])

    # Get innovation numbers for all connections using scan
    def get_single_innovation(carry, connection_pair):
        tracker, innovation_nums = carry
        sender, receiver = connection_pair
        sender_array = jnp.array([sender], dtype=jnp.int32)
        receiver_array = jnp.array([receiver], dtype=jnp.int32)
        innovation_id, updated_tracker = tracker.get_innovation_id(sender_array, receiver_array)
        return (updated_tracker, innovation_nums), innovation_id
    
    # Use scan for the innovation tracking loop
    connection_pairs = jnp.stack([senders, receivers], axis=1)
    initial_innovation_nums = jnp.zeros(total_connections, dtype=jnp.int32)
    (updated_tracker, _), innovation_nums = jax.lax.scan(
        get_single_innovation,
        (innovation_tracker, initial_innovation_nums),
        connection_pairs
    )

    # Generate random weights
    key, subkey = jax.random.split(key)
    weights = jax.random.normal(
        subkey,
        shape=(pop_size, total_connections),
        dtype=jnp.float32
    ) * weight_init_std + weight_init_mean

    # Fill in connection data for each genome using direct array operations
    # Set connection data for all genomes at once - keep proper types and avoid redundant casting
    connections = connections.at[:, :total_connections, 0].set(senders[None, :])  # Broadcast senders to all genomes
    connections = connections.at[:, :total_connections, 1].set(receivers[None, :])  # Broadcast receivers to all genomes
    connections = connections.at[:, :total_connections, 2].set(weights)  # Set weights per genome
    connections = connections.at[:, :total_connections, 3].set(innovation_nums[None, :])  # Broadcast innovation_nums to all genomes
    
    # Enable all initial connections for all genomes
    enabled = enabled.at[:, :total_connections].set(True)

    return Population(connections=connections, enabled=enabled, num_connections=num_connections), updated_tracker

def convert_genomes_to_networks(
    connections: jnp.ndarray,
    enabled: jnp.ndarray,
    config: NEATConfig
) -> tuple[NetworkBatch, jnp.ndarray]:
    """Convert a batch of genome arrays to a NetworkBatch.

    Args:
        connections: Array of shape [batch_size, max_connections, 4] containing
                   connection data (sender, receiver, weight, innovation_id)
        enabled: Boolean array of shape [batch_size, max_connections] indicating
                which connections are active
        config: NEATConfig instance containing network configuration

    Note:
        Hidden and output activation functions are taken from config.network.hidden_activation
        and config.network.output_activation respectively.

    Returns:
        tuple: (network_batch, genome_indices):
            network_batch: A NetworkBatch containing all networks with batched arrays
            genome_indices: Array of indices corresponding to the original genomes
            
    Raises:
        ValueError: If connections array has 0 connections (empty network)
    """
    # Validate input - networks must have at least some connections
    if connections.shape[1] == 0:
        raise ValueError("Cannot convert genomes with empty connections array")
    # Get parameters from config
    num_inputs = config.network.num_inputs
    num_outputs = config.network.num_outputs
    max_nodes = config.network.max_nodes
    hidden_activation = config.network.hidden_activation
    output_activation = config.network.output_activation
    
    # Get activation function codes from config using the centralized mapping
    hidden_activation = ACT_FN_MAP.get(hidden_activation, ACT_FN_MAP['relu'])  # Default to relu
    output_activation = ACT_FN_MAP.get(output_activation, ACT_FN_MAP['sigmoid'])  # Default to sigmoid
    
    # Get the batch size from the connections array
    batch_size = connections.shape[0]
    genome_indices = jnp.arange(batch_size, dtype=jnp.int32)

    def single_network(idx):
        """Convert a single genome to a Network object."""
        conn = connections[idx]
        en = enabled[idx]

        # Apply the enabled mask to connections
        # Disabled connections have their sender/receiver set to EMPTY_SLOT
        # First create a mask for the sender and receiver columns (0 and 1)
        conn_mask = jnp.zeros_like(conn)
        # Set sender and receiver columns to EMPTY_SLOT for disabled connections
        conn_mask = conn_mask.at[:, 0].set(EMPTY_SLOT)
        conn_mask = conn_mask.at[:, 1].set(EMPTY_SLOT)
        # Apply the mask where connections are disabled
        masked_conn = jnp.where(en[:, None], conn, conn_mask)

        # Extract node indices from senders and receivers
        senders = masked_conn[:, 0].astype(jnp.int32)
        receivers = masked_conn[:, 1].astype(jnp.int32)

        # Create array for all possible node indices
        all_nodes = jnp.arange(max_nodes)

        # Assign base node types based on node indices
        # Node 0 is bias, nodes 1..num_inputs are inputs,
        # nodes num_inputs+1..num_inputs+num_outputs are outputs,
        # and the rest are hidden nodes
        base_types = jnp.where(
            all_nodes == 0,
            NODE_BIAS,  # Bias node (index 0)
            jnp.where(
                all_nodes < num_inputs + 1,
                NODE_INPUT,  # Input nodes (indices 1 to num_inputs)
                jnp.where(
                    all_nodes < num_inputs + num_outputs + 1,
                    NODE_OUTPUT,  # Output nodes
                    NODE_HIDDEN   # Hidden nodes
                )
            )
        )

        # Determine which nodes are actually used in the network
        # A node is used if it appears as either a sender or receiver in any enabled connection
        is_sender = (all_nodes[:, None] == senders[None, :]).any(axis=1)
        is_receiver = (all_nodes[:, None] == receivers[None, :]).any(axis=1)
        is_used = is_sender | is_receiver

        # Set final node types (unused nodes get NODE_UNUSED)
        node_types = jnp.where(is_used, base_types, NODE_UNUSED)

        # Assign activation functions to nodes using constants:
        # - Bias node (0): identity (ACTIVATION_IDENTITY)
        # - Input nodes: identity (ACTIVATION_IDENTITY) - no activation function
        # - Output nodes: configurable output activation
        # - Used hidden nodes: configurable hidden activation
        # - Unused nodes: -1 (no activation)
        activation_fns = jnp.where(
            all_nodes == 0,
            ACTIVATION_IDENTITY,  # Bias node: identity activation
            jnp.where(
                all_nodes < num_inputs + 1,
                ACTIVATION_IDENTITY,  # Input nodes: identity activation
                jnp.where(
                    (all_nodes >= num_inputs + 1) & (all_nodes < num_inputs + num_outputs + 1),
                    output_activation,  # Output nodes: configurable activation
                    jnp.where(
                        is_used,
                        hidden_activation,  # Used hidden nodes: configurable activation
                        -1  # Unused nodes: no activation
                    )
                )
            )
        )

        # Create and return the Network object
        return Network(
            connections=masked_conn,
            enabled=en,
            num_inputs=num_inputs,
            num_outputs=num_outputs,
            node_types=node_types,
            activation_fns=activation_fns,
            max_nodes=max_nodes
        )

    # Apply the single_network function to each genome in the batch using vmap
    # This returns a Network object with batched fields
    batched_network = jax.vmap(single_network)(genome_indices)
    
    # Create node_indices array for the batch
    node_indices = jnp.tile(jnp.arange(max_nodes)[None, :], (batch_size, 1))
    
    # Create a NetworkBatch directly from the batched fields
    return NetworkBatch(
        node_indices=node_indices,
        node_types=batched_network.node_types,
        connections=batched_network.connections,
        enabled=batched_network.enabled,
        activation_fns=batched_network.activation_fns,
        num_inputs=batched_network.num_inputs,
        num_outputs=batched_network.num_outputs,
        max_nodes=batched_network.max_nodes
    ), genome_indices
