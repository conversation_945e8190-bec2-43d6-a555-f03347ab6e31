{"cells": [{"cell_type": "markdown", "metadata": {"colab_type": "text", "id": "view-in-github"}, "source": ["<a href=\"https://colab.research.google.com/github/google/evojax/blob/main/examples/notebooks/GymnaxEvosax.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>\n", "\n", "This notebook showcases how to use any [`evosax`](https://github.com/RobertTLange/evosax) evolutionary optimization algorithm and [`gymnax`](https://github.com/RobertTLange/gymnax) RL tasks jointly with the EvoJAX neuroevolution pipeline."]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "h9X_5AQOd2To"}, "outputs": [], "source": ["# @title Install Packages\n", "\n", "from IPython.display import clear_output\n", "\n", "!pip install chex\n", "!pip install git+https://github.com/google/evojax.git@main\n", "!pip install git+https://github.com/RobertTLange/gymnax.git@main\n", "!pip install git+https://github.com/RobertTLange/evosax.git@main\n", "\n", "clear_output()"]}, {"cell_type": "code", "execution_count": 1, "metadata": {"id": "dzTaZyZ2n9l5"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["jax.devices():\n"]}, {"data": {"text/plain": ["[StreamExecutorGpuDevice(id=0, process_index=0),\n", " StreamExecutorGpuDevice(id=1, process_index=0),\n", " StreamExecutorGpuDevice(id=2, process_index=0),\n", " StreamExecutorGpuDevice(id=3, process_index=0)]"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["# @title Import Libraries\n", "\n", "import time\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from typing import Sequence, <PERSON><PERSON>\n", "\n", "import jax\n", "import jax.numpy as jnp\n", "import chex\n", "from flax import linen as nn\n", "\n", "from evojax import SimManager\n", "from evojax import ObsNormalizer\n", "from evojax.policy.base import PolicyNetwork\n", "from evojax.policy.base import PolicyState\n", "from evojax.task.base import TaskState\n", "from evojax.util import get_params_format_fn\n", "\n", "import os\n", "if 'COLAB_TPU_ADDR' in os.environ:\n", "    from jax.tools import colab_tpu\n", "    colab_tpu.setup_tpu()\n", "\n", "print('jax.devices():')\n", "jax.devices()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Import gymnax task and define MinAtar Policy"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from gymnax.utils.evojax_wrapper import GymnaxTask\n", "\n", "# Define the Gymnax Task\n", "env_name = \"Asterix-MinAtar\"\n", "max_steps = 500\n", "train_task = GymnaxTask(env_name, max_steps=max_steps, test=False)\n", "test_task = GymnaxTask(env_name, max_steps=max_steps, test=True)"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# Define the MinAtar CNN Policy\n", "class MinAtarCNN(nn.Module):\n", "    \"\"\"A general purpose conv net model.\"\"\"\n", "\n", "    hidden_dims: Sequence[int]\n", "    out_dim: int\n", "\n", "    @nn.compact\n", "    def __call__(self, x: chex.Array) -> chex.Array:\n", "        x = nn.Conv(\n", "            features=16,\n", "            kernel_size=(3, 3),\n", "            padding=\"SAME\",\n", "            strides=1,\n", "        )(x)\n", "        x = nn.relu(x)\n", "        x = x.reshape((x.shape[0], -1))\n", "        for hidden_dim in self.hidden_dims:\n", "            x = nn.relu(\n", "                nn.<PERSON><PERSON>(\n", "                    features=hidden_dim,\n", "                )(x)\n", "            )\n", "        x = nn.Dense(features=self.out_dim)(x)\n", "        return x\n", "\n", "\n", "class MinAtarPolicy(PolicyNetwork):\n", "    \"\"\"Deterministic CNN policy - greedy action selection.\"\"\"\n", "\n", "    def __init__(\n", "        self,\n", "        input_dim: Sequence[int],\n", "        hidden_dims: Sequence[int],\n", "        output_dim: int,\n", "    ):\n", "        self.input_dim = [1, *input_dim]\n", "        self.model = MinAtarCNN(hidden_dims=hidden_dims, out_dim=output_dim)\n", "        self.params = self.model.init(\n", "            jax.random.PR<PERSON><PERSON>ey(0), jnp.ones(self.input_dim)\n", "        )\n", "        self.num_params, format_params_fn = get_params_format_fn(self.params)\n", "        self._format_params_fn = jax.vmap(format_params_fn)\n", "        self._forward_fn = jax.vmap(self.model.apply)\n", "\n", "    def get_actions(\n", "        self, t_states: TaskState, params: chex.Array, p_states: PolicyState\n", "    ) -> <PERSON><PERSON>[chex.<PERSON>, PolicyState]:\n", "        params = self._format_params_fn(params)\n", "        obs = jnp.expand_dims(t_states.obs, axis=1)\n", "        activations = self._forward_fn(params, obs)\n", "        action = jnp.argmax(activations, axis=2).squeeze()\n", "        return action, p_states\n", "\n", "\n", "policy = MinAtarPolicy(\n", "    input_dim=train_task.obs_shape,\n", "    output_dim=train_task.num_actions,\n", "    hidden_dims=[32],\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Setup evosax strategy wrapper for OpenAI-ES"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["from evosax import Strategies\n", "from evosax.utils.evojax_wrapper import Evosax2JAX_Wrapper"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["es_config = {\n", "    \"maximize\": True,\n", "    \"centered_rank\": True,\n", "    \"lrate_init\": 0.01,\n", "    \"lrate_decay\": 0.999,\n", "    \"lrate_limit\": 0.001,\n", "    \"sigma_init\": 0.1,\n", "    \"sigma_decay\": 0.999,\n", "    \"sigma_limit\": 0.01\n", "}\n", "\n", "solver = Evosax2JAX_Wrapper(\n", "    Strategies[\"OpenES\"],\n", "    param_size=policy.num_params,\n", "    pop_size=256,\n", "    es_config=es_config,\n", "    seed=42,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Run the neuroevolution training loop"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["obs_normalizer = ObsNormalizer(\n", "        obs_shape=train_task.obs_shape, dummy=True\n", "    )\n", "sim_mgr = SimManager(\n", "    policy_net=policy,\n", "    train_vec_task=train_task,\n", "    valid_vec_task=test_task,\n", "    seed=42,\n", "    obs_normalizer=obs_normalizer,\n", "    pop_size=256,\n", "    use_for_loop=False,\n", "    n_repeats=4,\n", "    test_n_repeats=1,\n", "    n_evaluations=64,\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["START EVOLVING 51989 PARAMS.\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/cognition/home/<USER>/anaconda/envs/snippets/lib/python3.8/site-packages/jax/_src/ops/scatter.py:87: FutureWarning: scatter inputs have incompatible types: cannot safely cast value from dtype=int32 to dtype=bool. In future JAX releases this will result in an error.\n", "  warnings.warn(\"scatter inputs have incompatible types: cannot safely cast \"\n", "/cognition/home/<USER>/anaconda/envs/snippets/lib/python3.8/site-packages/jax/_src/dispatch.py:380: UserWarning: The jitted function <unnamed function> includes a pmap. Using jit-of-pmap can lead to inefficient data movement, as the outer jit does not preserve sharded data representations and instead collects input and output arrays onto a single device. Consider removing the outer jit unless you know what you're doing. See https://github.com/google/jax/issues/2926.\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["{'num_gens': 1, 'train_perf': 0.6416015625, 'test_perf': 0.421875}\n", "{'num_gens': 200, 'train_perf': 0.5029296875, 'test_perf': 0.53125}\n", "{'num_gens': 400, 'train_perf': 0.494140625, 'test_perf': 0.34375}\n", "{'num_gens': 600, 'train_perf': 0.103515625, 'test_perf': 0.84375}\n", "{'num_gens': 800, 'train_perf': 1.3564453125, 'test_perf': 1.0625}\n", "{'num_gens': 1000, 'train_perf': 0.97265625, 'test_perf': 1.59375}\n", "{'num_gens': 1200, 'train_perf': 2.9208984375, 'test_perf': 1.609375}\n", "{'num_gens': 1400, 'train_perf': 2.2841796875, 'test_perf': 2.59375}\n", "{'num_gens': 1600, 'train_perf': 2.294921875, 'test_perf': 2.5}\n", "{'num_gens': 1800, 'train_perf': 4.8662109375, 'test_perf': 3.671875}\n", "{'num_gens': 2000, 'train_perf': 2.4375, 'test_perf': 4.96875}\n", "{'num_gens': 2200, 'train_perf': 3.634765625, 'test_perf': 3.703125}\n", "{'num_gens': 2400, 'train_perf': 3.8642578125, 'test_perf': 5.0625}\n", "{'num_gens': 2600, 'train_perf': 5.5986328125, 'test_perf': 4.5}\n", "{'num_gens': 2800, 'train_perf': 3.359375, 'test_perf': 5.59375}\n", "{'num_gens': 3000, 'train_perf': 4.1484375, 'test_perf': 4.734375}\n", "{'num_gens': 3200, 'train_perf': 5.390625, 'test_perf': 4.984375}\n", "{'num_gens': 3400, 'train_perf': 5.4306640625, 'test_perf': 5.046875}\n", "{'num_gens': 3600, 'train_perf': 6.533203125, 'test_perf': 4.953125}\n", "{'num_gens': 3800, 'train_perf': 6.533203125, 'test_perf': 6.890625}\n", "{'num_gens': 4000, 'train_perf': 8.615234375, 'test_perf': 8.03125}\n", "{'num_gens': 4200, 'train_perf': 9.240234375, 'test_perf': 6.203125}\n", "{'num_gens': 4400, 'train_perf': 7.7177734375, 'test_perf': 7.0}\n", "{'num_gens': 4600, 'train_perf': 10.5078125, 'test_perf': 6.671875}\n", "{'num_gens': 4800, 'train_perf': 6.5283203125, 'test_perf': 7.25}\n", "{'num_gens': 5000, 'train_perf': 6.26171875, 'test_perf': 7.796875}\n"]}], "source": [" print(f\"START EVOLVING {policy.num_params} PARAMS.\")\n", "# Run ES Loop.\n", "num_generations = 5000\n", "for gen_counter in range(num_generations):\n", "    \n", "    params = solver.ask()\n", "    scores, _ = sim_mgr.eval_params(params=params, test=False)\n", "    solver.tell(fitness=scores)\n", "\n", "    if gen_counter == 0 or (gen_counter + 1) % 200 == 0:\n", "        test_scores, _ = sim_mgr.eval_params(\n", "            params=solver.best_params, test=True\n", "        )\n", "        print(\n", "            {\n", "                \"num_gens\": gen_counter + 1,\n", "                \"train_perf\": float(np.nanmean(scores)),\n", "                \"test_perf\": float(np.nanmean(test_scores)),\n", "            },\n", "        )"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"accelerator": "TPU", "colab": {"collapsed_sections": [], "include_colab_link": true, "name": "EvoJAX Brax Example", "provenance": []}, "kernelspec": {"display_name": "snippets", "language": "python", "name": "snippets"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.11"}, "vscode": {"interpreter": {"hash": "31f2aee4e71d21fbe5cf8b01ff0e069b9275f58929596ceb00d14d90e3e16cd6"}}}, "nbformat": 4, "nbformat_minor": 1}