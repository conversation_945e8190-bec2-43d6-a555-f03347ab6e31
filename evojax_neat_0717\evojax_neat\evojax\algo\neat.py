"""
NEAT (NeuroEvolution of Augmenting Topologies) algorithm for EvoJAX.

This module provides the EvoJAX interface for the NEAT algorithm, allowing it to be used
with EvoJAX's training infrastructure while leveraging the full power of the NEAT
implementation in neat_core.
"""

import logging
import numpy as np
from typing import Union, Tuple, Optional, Dict, Any

import jax
import jax.numpy as jnp

from evojax.algo.base import NEAlgorithm
from evojax.util import create_logger
from .neat_core import (
    NEATConfig,
    XOR_CONFIG,
    Population,
    InnovationTracker,
    SpeciesState,
    NetworkBatch,
    initialize_population,
    initialize_species_state,
    convert_genomes_to_networks,
    evolve_population
)
from .neat_core.default_config import DEFAULT_NEAT_CONFIG, create_neat_config


class NEAT(NEAlgorithm):
    """NEAT algorithm implementing the EvoJAX NEAlgorithm interface.
    
    This class wraps the NEAT core implementation to provide compatibility with
    EvoJAX's ask/tell interface while maintaining the full functionality of NEAT
    including speciation, structural mutations, and hybrid training.
    """

    def __init__(self,
                 config: Optional[NEATConfig] = None,
                 seed: int = 0,
                 logger: Optional[logging.Logger] = None):
        """Initialize the NEAT algorithm.

        Args:
            config: NEAT configuration object. If None, uses DEFAULT_NEAT_CONFIG as default.
            seed: Random seed for reproducibility.
            logger: Logger instance. If None, creates a default logger.
        """
        
        if logger is None:
            self.logger = create_logger(name='NEAT')
        else:
            self.logger = logger

        # Use provided config or default to DEFAULT_NEAT_CONFIG
        if config is None:
            self.config = DEFAULT_NEAT_CONFIG
            self.logger.info("Using DEFAULT_NEAT_CONFIG for NEAT")
        else:
            self.config = config

        # Store configuration parameters
        self.pop_size = self.config.population.population_size
        self.param_size = self._calculate_param_size()
        
        # Initialize random key
        self.rand_key = jax.random.PRNGKey(seed=seed)
        
        # Initialize NEAT components
        self.innovation_tracker = InnovationTracker.create(self.config.network)
        self.population = None
        self.species_state = None
        self.networks = None
        self._best_params = None
        self._generation = 0
        self._current_fitness = None
        
        # Initialize population
        self._initialize_population()
        
        self.logger.info(f"NEAT initialized with population size: {self.pop_size}")
        self.logger.info(f"Network architecture: {self.config.network.num_inputs} inputs, "
                        f"{self.config.network.num_outputs} outputs")

    def _calculate_param_size(self) -> int:
        """Calculate the parameter size for EvoJAX compatibility.
        
        For NEAT, this is a bit tricky since networks have variable structure.
        We'll use the maximum possible connections as an approximation.
        """
        max_nodes = self.config.network.max_nodes
        max_connections = self.config.network.max_connections
        # Each connection has: sender, receiver, weight, innovation_id
        # Plus node types and activation functions
        return max_connections * 4 + max_nodes * 2

    def _initialize_population(self) -> None:
        """Initialize the NEAT population and species."""
        # Split random key
        self.rand_key, pop_key, species_key = jax.random.split(self.rand_key, 3)
        
        # Initialize population
        self.population, self.innovation_tracker = initialize_population(
            key=pop_key,
            innovation_tracker=self.innovation_tracker,
            config=self.config
        )

        # Initialize species state
        self.species_state = initialize_species_state(
            connections=self.population.connections,
            key=species_key,
            config=self.config.species
        )
        
        # Convert to networks for evaluation
        self.networks, _ = convert_genomes_to_networks(
            connections=self.population.connections,
            enabled=self.population.enabled,
            config=self.config
        )
        
        self.logger.debug(f"Initialized population with {self.pop_size} genomes")

    def _population_to_params(self) -> jnp.ndarray:
        """Convert NEAT population to EvoJAX parameter format.
        
        This is a simplified conversion that flattens the network structures
        into fixed-size parameter vectors for EvoJAX compatibility.
        """
        # For now, we'll create a simplified parameter representation
        # This could be improved to better represent the actual network structure
        batch_size = self.population.connections.shape[0]
        
        # Flatten connections and enabled arrays
        connections_flat = self.population.connections.reshape(batch_size, -1)
        enabled_flat = self.population.enabled.reshape(batch_size, -1)
        
        # Combine into parameter vectors
        params = jnp.concatenate([
            connections_flat.astype(jnp.float32),
            enabled_flat.astype(jnp.float32)
        ], axis=1)
        
        # Pad or truncate to match param_size
        if params.shape[1] < self.param_size:
            padding = jnp.zeros((batch_size, self.param_size - params.shape[1]))
            params = jnp.concatenate([params, padding], axis=1)
        elif params.shape[1] > self.param_size:
            params = params[:, :self.param_size]
            
        return params

    def ask(self) -> jnp.ndarray:
        """Ask the algorithm for a population of parameters.
        
        Returns:
            A JAX array of shape (population_size, param_size) representing
            the current population in EvoJAX parameter format.
        """
        if self.networks is None:
            self._initialize_population()
            
        # Convert NEAT population to EvoJAX parameter format
        params = self._population_to_params()
        
        self.logger.debug(f"Generated parameters for generation {self._generation}")
        return params

    def tell(self, fitness: jnp.ndarray) -> None:
        """Update population based on fitness scores."""
        # Store fitness for use in evolution
        self._current_fitness = fitness
        
        # Create a fitness function that works with the NEAT core signature
        # The NEAT core will call this for each network during evaluation
        fitness_counter = {'count': 0}
        
        def fitness_fn(network, activation_state, inputs):
            # Use a counter to map to the correct fitness value
            # This assumes networks are evaluated in order
            idx = fitness_counter['count']
            fitness_counter['count'] = (fitness_counter['count'] + 1) % len(self._current_fitness)
            return self._current_fitness[idx]
        
        # Reset counter before evolution
        fitness_counter['count'] = 0
        
        # Create dummy inputs (just need correct shape)
        dummy_inputs = jnp.zeros((1, self.config.network.num_inputs))
        
        # Run actual NEAT evolution using the neat_core
        self.networks, self.species_state, self.innovation_tracker, _, self.rand_key = evolve_population(
            networks=self.networks,
            species_state=self.species_state, 
            tracker=self.innovation_tracker,
            key=self.rand_key,
            fitness_fn=fitness_fn,
            inputs=dummy_inputs,
            config=self.config
        )
        
        self._generation += 1
        
        # Log progress
        best_fitness = jnp.max(fitness)
        mean_fitness = jnp.mean(fitness)
        self.logger.info(f"Generation {self._generation}: Best fitness: {best_fitness:.4f}, Mean fitness: {mean_fitness:.4f}")

    @property
    def best_params(self) -> jnp.ndarray:
        """Get the best parameters found so far."""
        if self._best_params is None:
            # Return zeros if no best params yet
            return jnp.zeros(self.param_size)
        return self._best_params

    @best_params.setter
    def best_params(self, params: Union[np.ndarray, jnp.ndarray]) -> None:
        """Set the best parameters (for initialization purposes)."""
        self._best_params = jnp.asarray(params)

    def save_state(self) -> Dict[str, Any]:
        """Save the current state of the NEAT algorithm."""
        return {
            'population': self.population,
            'species_state': self.species_state,
            'innovation_tracker': self.innovation_tracker,
            'generation': self._generation,
            'best_params': self._best_params,
            'rand_key': self.rand_key
        }

    def load_state(self, saved_state: Dict[str, Any]) -> None:
        """Load a previously saved state."""
        self.population = saved_state['population']
        self.species_state = saved_state['species_state']
        self.innovation_tracker = saved_state['innovation_tracker']
        self._generation = saved_state['generation']
        self._best_params = saved_state['best_params']
        self.rand_key = saved_state['rand_key']
        
        # Regenerate networks from loaded population
        self.networks, _ = convert_genomes_to_networks(
            connections=self.population.connections,
            enabled=self.population.enabled,
            config=self.config
        )

    def get_networks(self) -> NetworkBatch:
        """Get the current population as a NetworkBatch for direct evaluation.
        
        This method provides access to the actual NEAT networks, which can be
        more efficient than using the EvoJAX parameter interface for some tasks.
        """
        return self.networks

    def get_population_info(self) -> Dict[str, Any]:
        """Get information about the current population state."""
        return {
            'generation': self._generation,
            'population_size': self.pop_size,
            'num_species': jnp.sum(self.species_state.active_mask),
            'innovation_count': self.innovation_tracker.next_innovation_id,
            'config': self.config
        }

