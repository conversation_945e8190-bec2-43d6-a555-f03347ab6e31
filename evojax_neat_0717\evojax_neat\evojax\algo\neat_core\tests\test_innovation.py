import jax
import jax.numpy as jnp
import pytest
from flax import struct
from dataclasses import dataclass

from ..innovation import InnovationTracker
from ..config.base import NetworkConfig, NEATConfig, MutationConfig

@struct.dataclass
class NetworkConfigTest(NetworkConfig):
    """Test configuration for network parameters."""
    max_connections: int = 100
    num_inputs: int = 3
    num_outputs: int = 2
    max_nodes: int = 100
    weight_init_std: float = 0.1
    weight_init_mean: float = 0.0

@pytest.fixture
def test_config() -> NetworkConfigTest:
    """Create a test configuration with all necessary components."""
    return NetworkConfigTest()

@pytest.fixture
def tracker(test_config) -> InnovationTracker:
    """Create an innovation tracker for testing."""
    return InnovationTracker.create(test_config)

def test_innovation_tracker_create(test_config):
    """Test creation of innovation tracker with config."""
    tracker = InnovationTracker.create(test_config)
    assert tracker.next_innovation_id == 0
    assert tracker.connection_history.shape == (test_config.max_connections, 3)
    assert tracker.connection_history.dtype == jnp.int32

def test_get_innovation_id_new_connection(tracker):
    """Test getting innovation ID for a new connection."""
    sender = jnp.array([1], dtype=jnp.int32)
    receiver = jnp.array([2], dtype=jnp.int32)

    innovation_id, updated_tracker = tracker.get_innovation_id(sender, receiver)

    assert innovation_id == 0
    assert jnp.array_equal(updated_tracker.next_innovation_id, jnp.array(1, dtype=jnp.int32))
    assert jnp.array_equal(
        updated_tracker.connection_history[0],
        jnp.array([1, 2, 0], dtype=jnp.int32)
    )
    assert jnp.all(updated_tracker.connection_history[1:] == -1)

def test_get_innovation_id_existing_connection(tracker):
    """Test getting innovation ID for an existing connection."""
    sender = jnp.array([1], dtype=jnp.int32)
    receiver = jnp.array([2], dtype=jnp.int32)

    # Add first connection
    first_id, tracker = tracker.get_innovation_id(sender, receiver)
    # Request same connection again
    second_id, updated_tracker = tracker.get_innovation_id(sender, receiver)

    assert first_id == 0
    assert second_id == 0  # Should return same ID
    assert jnp.array_equal(updated_tracker.next_innovation_id, jnp.array(1, dtype=jnp.int32))
    assert jnp.array_equal(
        updated_tracker.connection_history[0],
        jnp.array([1, 2, 0], dtype=jnp.int32)
    )
    assert jnp.all(updated_tracker.connection_history[1:] == -1)

def test_jit_compatibility(tracker):
    """Test that the innovation tracker works with JAX JIT compilation."""
    @jax.jit
    def jitted_get_innovation(tracker, sender, receiver):
        return tracker.get_innovation_id(sender, receiver)

    sender = jnp.array([1], dtype=jnp.int32)
    receiver = jnp.array([2], dtype=jnp.int32)

    # First call - should compile and run
    innovation_id, updated_tracker = jitted_get_innovation(tracker, sender, receiver)
    assert innovation_id == 0

    # Second call - should reuse existing connection
    innovation_id, updated_tracker = jitted_get_innovation(updated_tracker, sender, receiver)
    assert innovation_id == 0

def test_max_capacity_handling():
    """Test behavior when connection history reaches max capacity."""
    # Create a config with small max_connections for testing
    small_config = NetworkConfigTest(max_connections=3)
    tracker = InnovationTracker.create(small_config)

    # Fill up the connection history
    for i in range(small_config.max_connections):
        innovation_id, tracker = tracker.get_innovation_id(
            jnp.array([i], dtype=jnp.int32),
            jnp.array([i + 1], dtype=jnp.int32)
        )
        assert innovation_id >= 0  # Should be valid innovation IDs

    # Verify we've used all available slots
    assert jnp.array_equal(tracker.next_innovation_id, jnp.array(small_config.max_connections, dtype=jnp.int32))
    assert tracker.connection_history.shape == (small_config.max_connections, 3)

    # Next call should return -1 as innovation ID when at capacity
    innovation_id, updated_tracker = tracker.get_innovation_id(
        jnp.array([small_config.max_connections], dtype=jnp.int32),
        jnp.array([small_config.max_connections + 1], dtype=jnp.int32)
    )
    assert innovation_id == -1
    assert updated_tracker.next_innovation_id == tracker.next_innovation_id  # Should be unchanged
    assert jnp.array_equal(updated_tracker.connection_history, tracker.connection_history)  # Should be unchanged

def test_innovation_id_uniqueness(tracker):
    """Test that innovation IDs are unique for different connections."""
    # Create several different connections
    connections = [
        (jnp.array([1], dtype=jnp.int32), jnp.array([2], dtype=jnp.int32)),
        (jnp.array([2], dtype=jnp.int32), jnp.array([3], dtype=jnp.int32)),
        (jnp.array([1], dtype=jnp.int32), jnp.array([3], dtype=jnp.int32)),
    ]

    # Get innovation IDs for each connection
    innovation_ids = []
    current_tracker = tracker
    for sender, receiver in connections:
        innovation_id, current_tracker = current_tracker.get_innovation_id(sender, receiver)
        # Convert JAX scalar to Python int
        innovation_ids.append(int(innovation_id))

    # Verify all innovation IDs are unique
    assert len(set(innovation_ids)) == len(innovation_ids)
    assert innovation_ids == [0, 1, 2]  # Should be assigned in order

def test_connection_history_ordering(tracker):
    """Test that connection history maintains proper ordering."""
    # Create connections in a specific order
    connections = [
        (jnp.array([1], dtype=jnp.int32), jnp.array([2], dtype=jnp.int32)),
        (jnp.array([3], dtype=jnp.int32), jnp.array([4], dtype=jnp.int32)),
        (jnp.array([5], dtype=jnp.int32), jnp.array([6], dtype=jnp.int32)),
    ]

    # Add connections and verify history
    current_tracker = tracker
    for i, (sender, receiver) in enumerate(connections):
        _, current_tracker = current_tracker.get_innovation_id(sender, receiver)
        # Verify this connection is in the history at the correct position
        assert jnp.array_equal(
            current_tracker.connection_history[i],
            jnp.array([sender[0], receiver[0], i], dtype=jnp.int32)
        )
        # Verify remaining slots are empty
        assert jnp.all(current_tracker.connection_history[i+1:] == -1)

if __name__ == "__main__":
    # Run all tests using pytest
    import sys
    import pytest
    sys.exit(pytest.main([__file__] + sys.argv[1:]))