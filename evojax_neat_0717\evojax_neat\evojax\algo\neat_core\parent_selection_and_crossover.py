import jax
import jax.numpy as jnp
import chex
from functools import partial

from .species import SpeciesState
from .config.base import RecombinationConfig

@partial(jax.jit, static_argnames=['config'])
def select_parents(
    species_state: SpeciesState,
    ranks: jnp.ndarray,
    key: chex.PRNGKey,
    config: RecombinationConfig
) -> jnp.ndarray:
    """Select parents using tournament selection within species.
    
    Args:
        species_state: Current speciation state
        ranks: Pre-computed ranks where lower values are better
        key: Random key for reproducibility
        config: Recombination configuration
        
    Returns:
        Array of parent pairs [population_size, 2]
    """
    # Extract config parameters
    tournament_size = config.tournament_size
    
    # Extract species data
    member_masks = species_state.member_masks  # Shape: (max_species, pop_size)
    offspring_counts = species_state.offspring_counts  # Shape: (max_species,)
    max_species, pop_size = member_masks.shape
    
    # Initialize parent pairs array
    parent_pairs = jnp.zeros((pop_size, 2), dtype=jnp.int32)
    current_offset = 0
    
    def select_parents_for_species(species_idx, carry):
        parent_pairs, offset, current_key = carry
        
        # Get species members
        species_mask = member_masks[species_idx]
        n_offspring = offspring_counts[species_idx].astype(jnp.int32)
        
        # Create indices for tournament selection
        species_indices = jnp.where(species_mask, jnp.arange(pop_size), pop_size)
        
        def run_single_tournament(i, state):
            pairs, key = state
            tournament_key, next_key = jax.random.split(key)
            
            # Select tournament participants randomly from species members
            # Use categorical sampling instead of choice for fixed shapes
            tournament_key1, tournament_key2 = jax.random.split(tournament_key)
            
            # Create probability distribution over species members
            probs = species_mask.astype(jnp.float32)
            probs = probs / jnp.maximum(jnp.sum(probs), 1e-8)  # Normalize, avoid division by zero
            
            # Sample tournament participants using categorical distribution
            tournament1_indices = jax.random.categorical(
                tournament_key1, 
                jnp.log(probs + 1e-8),  # Add small epsilon for numerical stability
                shape=(tournament_size,)
            )
            tournament2_indices = jax.random.categorical(
                tournament_key2,
                jnp.log(probs + 1e-8),
                shape=(tournament_size,)
            )
            
            # Select winners (lower rank = better)
            tournament1_ranks = ranks[tournament1_indices]
            tournament2_ranks = ranks[tournament2_indices]
            
            parent1 = tournament1_indices[jnp.argmin(tournament1_ranks)]
            parent2 = tournament2_indices[jnp.argmin(tournament2_ranks)]
            
            # Ensure parent1 is fitter (lower rank)
            parent1_rank = ranks[parent1]
            parent2_rank = ranks[parent2]
            
            better_parent = jnp.where(parent1_rank <= parent2_rank, parent1, parent2)
            worse_parent = jnp.where(parent1_rank <= parent2_rank, parent2, parent1)
            
            # Update parent pairs at current position
            updated_pairs = pairs.at[offset + i].set(jnp.array([better_parent, worse_parent]))
            
            return (updated_pairs, next_key)
        
        # Only run tournaments if we need offspring for this species
        def run_tournaments():
            return jax.lax.fori_loop(
                0, n_offspring,
                run_single_tournament,
                (parent_pairs, current_key)
            )
        
        def skip_tournaments():
            return (parent_pairs, current_key)
        
        # Conditionally run tournaments based on offspring count
        updated_pairs, final_key = jax.lax.cond(
            n_offspring > 0,
            run_tournaments,
            skip_tournaments
        )
        
        return (updated_pairs, offset + n_offspring, final_key)
    
    # Process all species
    def process_all_species(i, carry):
        return select_parents_for_species(i, carry)
    
    final_pairs, _, _ = jax.lax.fori_loop(
        0, max_species,
        process_all_species,
        (parent_pairs, 0, key)
    )
    
    return final_pairs

@partial(jax.jit, static_argnames=['config'])
def crossover(
    parent_pairs: jnp.ndarray,
    connections: jnp.ndarray,
    enabled: jnp.ndarray,
    key: chex.PRNGKey,
    config: RecombinationConfig
) -> tuple[jnp.ndarray, jnp.ndarray]:
    """Perform crossover for the entire population.
    
    Args:
        parent_pairs: Array of parent indices [population_size, 2]
        connections: Array of connection genes [population_size, max_connections, 4]
        enabled: Boolean array indicating which connections are enabled
        key: Random key for reproducibility
        config: Recombination configuration
    
    Returns:
        tuple of (offspring_connections, offspring_enabled)
    """
    # Extract config parameters
    parent1_gene_rate = config.parent1_gene_rate
    
    pop_size = parent_pairs.shape[0]
    max_connections = connections.shape[1]
    
    # Initialize offspring arrays
    offspring_connections = jnp.zeros_like(connections)
    offspring_enabled = jnp.zeros_like(enabled)
    
    def crossover_single_pair(i, carry):
        off_conn, off_enabled, current_key = carry
        
        # Get parent indices
        parent1_idx = parent_pairs[i, 0]
        parent2_idx = parent_pairs[i, 1]
        
        # Get parent genomes
        parent1_connections = connections[parent1_idx]
        parent2_connections = connections[parent2_idx]
        parent1_enabled = enabled[parent1_idx]
        parent2_enabled = enabled[parent2_idx]
        
        # Generate crossover key
        crossover_key, next_key = jax.random.split(current_key)
        
        # Start with parent1 as base
        child_connections = parent1_connections
        child_enabled = parent1_enabled
        
        # Find matching innovations between parents
        parent1_innovations = parent1_connections[:, 3]
        parent2_innovations = parent2_connections[:, 3]
        
        # Process each connection gene
        def process_connection(j, state):
            child_conn, child_enab = state
            
            # Check if this innovation exists in parent2
            p1_innovation = parent1_innovations[j]
            matching_mask = parent2_innovations == p1_innovation
            has_match = jnp.any(matching_mask)
            
            def apply_crossover():
                # Find the matching connection in parent2
                p2_match_idx = jnp.argmax(matching_mask)
                
                # Decide whether to inherit from parent1 or parent2
                inherit_key = jax.random.fold_in(crossover_key, j)
                use_parent2 = jax.random.uniform(inherit_key) > parent1_gene_rate
                
                # Update weight from parent2 if selected
                new_weight = jnp.where(
                    use_parent2,
                    parent2_connections[p2_match_idx, 2],
                    parent1_connections[j, 2]
                )
                
                # Update connection
                updated_conn = child_conn.at[j, 2].set(new_weight)
                
                return (updated_conn, child_enab)
            
            def keep_parent1():
                return (child_conn, child_enab)
            
            # Only apply crossover if both parents have this innovation
            return jax.lax.cond(
                has_match & parent1_enabled[j],
                apply_crossover,
                keep_parent1
            )
        
        # Process all connections
        final_connections, final_enabled = jax.lax.fori_loop(
            0, max_connections,
            process_connection,
            (child_connections, child_enabled)
        )
        
        # Update offspring arrays
        updated_off_conn = off_conn.at[i].set(final_connections)
        updated_off_enabled = off_enabled.at[i].set(final_enabled)
        
        return (updated_off_conn, updated_off_enabled, next_key)
    
    # Process all parent pairs
    final_connections, final_enabled, _ = jax.lax.fori_loop(
        0, pop_size,
        crossover_single_pair,
        (offspring_connections, offspring_enabled, key)
    )
    
    return final_connections, final_enabled

@partial(jax.jit, static_argnames=['config'])
def recombine(
    species_state: SpeciesState,
    connections: jnp.ndarray,
    enabled: jnp.ndarray,
    fitness: jnp.ndarray,
    key: chex.PRNGKey,
    config: RecombinationConfig
) -> tuple[jnp.ndarray, jnp.ndarray]:
    """Perform recombination with elitism and culling.
    
    Args:
        species_state: Current speciation state
        connections: Connection genes [population_size, max_connections, 4]
        enabled: Enabled status for connections [population_size, max_connections]
        fitness: Fitness values [population_size]
        key: Random key
        config: Recombination configuration
        
    Returns:
        tuple of (new_connections, new_enabled)
    """
    # Extract config parameters
    cull_ratio = config.cull_ratio
    elite_ratio = config.elite_ratio
    
    pop_size = connections.shape[0]
    
    # Sort by fitness (best first)
    sorted_indices = jnp.argsort(-fitness)
    
    # Calculate elite and cull counts
    num_elites = jnp.ceil(elite_ratio * pop_size).astype(jnp.int32)
    num_cull = jnp.floor(cull_ratio * pop_size).astype(jnp.int32)
    
    # Compute rank for each individual (0 = best, pop_size-1 = worst)
    rank = jnp.empty_like(sorted_indices)
    rank = rank.at[sorted_indices].set(jnp.arange(pop_size))
    
    # Create masks
    cull_mask = rank >= (pop_size - num_cull)
    non_cull_mask = ~cull_mask
    
    # Elite mask: assign elite_rank among non-culled
    elite_rank = jnp.where(non_cull_mask, jnp.cumsum(non_cull_mask) - 1, -1)
    elite_mask = (elite_rank >= 0) & (elite_rank < num_elites)
    
    # Parent candidate mask: non-culled, non-elite
    parent_candidate_mask = non_cull_mask & (~elite_mask)
    
    # Filter species membership to only include parent candidates
    filtered_member_masks = species_state.member_masks & parent_candidate_mask[None, :]
    parent_species_state = species_state.replace(member_masks=filtered_member_masks)
    
    # Select parents and perform crossover
    parent_select_key, crossover_key = jax.random.split(key)
    
    parent_pairs = select_parents(
        species_state=parent_species_state,
        ranks=rank,  # Use rank instead of sorted_indices
        key=parent_select_key,
        config=config
    )
    
    offspring_connections, offspring_enabled = crossover(
        parent_pairs=parent_pairs,
        connections=connections,
        enabled=enabled,
        key=crossover_key,
        config=config
    )
    
    # Assemble final population: elites + offspring
    new_connections = jnp.where(
        elite_mask[:, None, None],
        connections,  # Keep elite connections
        offspring_connections  # Use offspring for non-elites
    )
    
    new_enabled = jnp.where(
        elite_mask[:, None],
        enabled,  # Keep elite enabled status
        offspring_enabled  # Use offspring for non-elites
    )
    
    return new_connections, new_enabled