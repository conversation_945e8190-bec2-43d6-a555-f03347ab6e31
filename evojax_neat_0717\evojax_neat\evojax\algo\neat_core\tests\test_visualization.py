"""Tests for the visualization module."""

import pytest
import jax
import jax.numpy as jnp
import matplotlib.pyplot as plt
import graphviz
from typing import Dict, Any
from pathlib import Path

from ..visualization import (
    plot_fitness_history,
    visualize_network,
    create_visualization_hook
)
from ..network import Network, NetworkBatch
from ..species import initialize_state, SpeciesState
from ..population import initialize_population, convert_genomes_to_networks
from ..innovation import InnovationTracker
from ..config.base import NEATConfig, NetworkConfig, SpeciesConfig, PopulationConfig

@pytest.fixture
def sample_networks() -> Dict[str, Network]:
    """Create multiple sample networks with different architectures for testing."""
    networks = {}
    
    # 1. Simple XOR-like network (2 inputs, 2 hidden, 1 output)
    networks['xor'] = Network(
        node_types=jnp.array([4, 0, 0, 1, 1, 2, -1, -1, -1, -1]),
        connections=jnp.array([
            # from, to, weight, innovation
            [0, 3, 1.0, 1],    # bias -> hidden 1
            [1, 3, 0.5, 2],    # input 1 -> hidden 1
            [2, 3, -0.5, 3],   # input 2 -> hidden 1
            [0, 4, 1.0, 4],    # bias -> hidden 2
            [1, 4, -0.5, 5],   # input 1 -> hidden 2
            [2, 4, 0.5, 6],    # input 2 -> hidden 2
            [3, 5, 0.8, 7],    # hidden 1 -> output
            [4, 5, 0.8, 8],    # hidden 2 -> output
            [0, 5, 0.2, 9],    # bias -> output (direct connection)
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
        ]),
        enabled=jnp.array([True, True, True, True, True, True, True, True, True] + [False] * 11),
        activation_fns=jnp.array([7, 7, 7, 1, 1, 0, 0, 0, 0, 0]),
        num_inputs=3,  # bias + 2 inputs
        num_outputs=1,
        max_nodes=10
    )
    
    # 2. Minimal network (1 input, 1 output, no hidden)
    networks['minimal'] = Network(
        node_types=jnp.array([4, 0, 2]),  # bias, input, output
        connections=jnp.array([
            [0, 2, 0.5, 1],  # bias -> output
            [1, 2, 0.8, 2],  # input -> output
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1]  # padding
        ]),
        enabled=jnp.array([True, True] + [False] * 18),
        activation_fns=jnp.array([7, 7, 0]),  # identity, identity, sigmoid
        num_inputs=2,  # bias + 1 input
        num_outputs=1,
        max_nodes=3
    )
    
    # 3. Deep network (for testing deeper architectures)
    networks['deep'] = Network(
        node_types=jnp.array([4, 0, 0, 1, 1, 1, 1, 2] + [-1] * 12),  # bias, 2 inputs, 4 hidden, 1 output
        connections=jnp.array([
            # Input to first hidden layer
            [0, 3, 1.0, 1],    # bias -> h1
            [1, 3, 0.5, 2],    # i1 -> h1
            [2, 3, -0.5, 3],   # i2 -> h1
            [0, 4, 1.0, 4],    # bias -> h2
            [1, 4, -0.5, 5],   # i1 -> h2
            [2, 4, 0.5, 6],    # i2 -> h2
            # First to second hidden layer
            [3, 5, 0.6, 7],    # h1 -> h3
            [4, 5, 0.4, 8],    # h2 -> h3
            [3, 6, 0.4, 9],    # h1 -> h4
            [4, 6, 0.6, 10],   # h2 -> h4
            # Second hidden to output
            [5, 7, 0.8, 11],   # h3 -> out
            [6, 7, 0.8, 12],   # h4 -> out
            [0, 7, 0.2, 13],   # bias -> out
            # Skip connections
            [1, 5, 0.3, 14],   # i1 -> h3
            [2, 6, 0.3, 15],   # i2 -> h4
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1]  # padding
        ]),
        enabled=jnp.array([True] * 15 + [False] * 5),  # Enable first 15 connections
        activation_fns=jnp.array([7, 7, 7, 1, 1, 1, 1, 0] + [0] * 12),  # tanh for hidden, sigmoid for output
        num_inputs=3,  # bias + 2 inputs
        num_outputs=1,
        max_nodes=20
    )
    
    # 4. Network with disabled connections
    networks['disabled'] = Network(
        node_types=jnp.array([4, 0, 0, 1, 2]),
        connections=jnp.array([
            [0, 3, 1.0, 1],    # bias -> h1 (enabled)
            [1, 3, 0.5, 2],    # i1 -> h1 (disabled)
            [2, 3, -0.5, 3],   # i2 -> h1 (enabled)
            [0, 4, 1.0, 4],    # bias -> out (enabled)
            [3, 4, 0.8, 5],    # h1 -> out (disabled)
            [1, 4, 0.6, 6],    # i1 -> out (enabled)
            [2, 4, 0.7, 7],    # i2 -> out (disabled)
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1], # padding
            [-1, -1, 0.0, -1]  # padding
        ]),
        enabled=jnp.array([True, False, True, True, False, True, False] + [False] * 13),
        activation_fns=jnp.array([7, 7, 7, 1, 0]),
        num_inputs=3,  # bias + 2 inputs
        num_outputs=1,
        max_nodes=5
    )
    
    return networks

@pytest.fixture
def sample_network(sample_networks: Dict[str, Network]) -> Network:
    """Return the default test network (XOR-like)."""
    return sample_networks['xor']

@pytest.fixture
def sample_fitness_history() -> jnp.ndarray:
    """Create sample fitness history data for testing."""
    return jnp.array([0.1, 0.2, 0.3, 0.5, 0.7, 0.9, 1.2, 1.5, 1.8, 2.0])

@pytest.fixture
def sample_species_counts() -> jnp.ndarray:
    """Create sample species count data for testing."""
    return jnp.array([1, 2, 3, 4, 5, 5, 4, 3, 3, 2])

@pytest.fixture
def sample_visualization_config() -> Dict[str, Any]:
    """Return sample visualization configuration."""
    return {
        'node_colors': {
            'input': '#FFCCCC',
            'hidden': '#CCFFCC',
            'output': '#CCCCFF',
            'bias': '#FFFFCC'
        },
        'edge_colors': {
            'enabled': '#000000',
            'disabled': '#CCCCCC',
            'recurrent': '#FF0000'
        },
        'node_size': 0.5,
        'font_size': 12,
        'width': 8.0,
        'height': 6.0,
        'dpi': 100
    }

def test_plot_fitness_history(sample_fitness_history: jnp.ndarray, 
                           sample_species_counts: jnp.ndarray,
                           tmp_path: Path):
    """Test that fitness history plotting works correctly."""
    # Test without species counts
    save_path = tmp_path / "fitness_plot.png"
    fig, ax = plot_fitness_history(
        fitness_history=sample_fitness_history,
        title="Test Fitness Plot",
        save_path=str(save_path)
    )
    
    assert save_path.exists()
    assert isinstance(fig, plt.Figure)
    assert isinstance(ax, plt.Axes)
    assert len(ax.lines) == 1  # Only fitness line
    assert ax.get_title() == "Test Fitness Plot"
    plt.close(fig)
    
    # Test with species counts
    save_path = tmp_path / "fitness_with_species.png"
    fig, ax = plot_fitness_history(
        fitness_history=sample_fitness_history,
        species_counts=sample_species_counts,
        title="Test Fitness Plot with Species",
        save_path=str(save_path)
    )
    
    assert save_path.exists()
    assert len(ax.lines) == 1  # Only fitness line on primary axis
    assert ax.get_ylabel() == "Fitness"
    assert ax.get_xlabel() == "Generation"
    assert ax.get_title() == "Test Fitness Plot with Species"
    plt.close(fig)
    
    # Test with empty fitness history - should handle gracefully or raise
    try:
        fig, ax = plot_fitness_history(fitness_history=jnp.array([]))
        # If it doesn't raise, that's also acceptable behavior
        plt.close(fig)
    except (ValueError, IndexError):
        # Expected behavior - empty array should raise an error
        pass

def test_visualize_network(sample_networks: Dict[str, Network], 
                         sample_visualization_config: Dict[str, Any],
                         tmp_path: Path):
    """Test that network visualization works correctly with different network architectures."""
    # Test each network type
    for net_name, network in sample_networks.items():
        save_path = tmp_path / f"{net_name}_network"
        
        # Test with default settings
        dot = visualize_network(
            network=network,
            title=f"{net_name.capitalize()} Network",
            save_path=str(save_path)
        )
        
        # Check output files and object types
        assert (tmp_path / f"{net_name}_network.png").exists()
        assert isinstance(dot, graphviz.Digraph)
        
        # Verify basic graph structure
        assert "digraph" in dot.source
        assert f"{net_name.capitalize()} Network" in dot.source
        
        # Test with custom settings
        save_path_custom = tmp_path / f"{net_name}_network_custom"
        dot = visualize_network(
            network=network,
            title=f"Custom {net_name.capitalize()} Network",
            node_colors=sample_visualization_config['node_colors'],
            show_weights=True,
            save_path=str(save_path_custom),
            format="svg"
        )
        
        assert (tmp_path / f"{net_name}_network_custom.svg").exists()
        assert "node [shape=circle" in dot.source  # Check node shape
        
        # Test with minimal settings (no save)
        dot = visualize_network(
            network=network,
            title=None,
            show_weights=False
        )
        assert isinstance(dot, graphviz.Digraph)
        assert "digraph" in dot.source

def test_visualization_hook(tmp_path: Path):
    """Test that the visualization hook works correctly with different configurations."""
    # Setup test directory
    output_dir = tmp_path / "viz_output"
    output_dir.mkdir()
    
    # Create hook with custom configuration
    hook = create_visualization_hook(
        output_dir=str(output_dir),
        plot_interval=2,
        network_interval=4,
        plot_species=True,
        max_networks_per_gen=2,
        format="png"
    )
    
    # Create test data
    key = jax.random.PRNGKey(42)
    
    # Create config for initialization
    config = NEATConfig.create(
        seed=42,
        max_generations=10,
        population=PopulationConfig(population_size=10),
        network=NetworkConfig(
            num_inputs=2,
            num_outputs=1,
            max_nodes=10,
            max_connections=10,
            activation_fn="sigmoid",
            hidden_activation="sigmoid",
            output_activation="sigmoid"
        ),
        species=SpeciesConfig(max_species=5)
    )
    
    tracker = InnovationTracker.create(config.network)
    
    # Create a small population
    population, tracker = initialize_population(
        key=key,
        innovation_tracker=tracker,
        config=config
    )
    
    # Convert to networks
    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=config
    )
    
    # Create species state
    key, species_key = jax.random.split(key)
    species_state = initialize_state(
        connections=population.connections,
        key=species_key,
        config=config.species
    )
    
    # Create fitness scores
    fitnesses = jnp.array([0.1, 0.2, 0.3, 0.4, 0.5, 0.6, 0.7, 0.8, 0.9, 1.0])
    best_fitness = 1.0
    best_idx = 9
    
    # Call hook for different generations
    for gen in range(6):
        hook(
            generation=gen,
            networks=networks,
            species_state=species_state,
            fitnesses=fitnesses,
            best_fitness=best_fitness,
            best_idx=best_idx
        )
    
    # Check that files were created at the right intervals
    # Fitness plots should be at generations 0, 2, 4
    assert (output_dir / "fitness_gen0000.png").exists()
    assert (output_dir / "fitness_gen0002.png").exists()
    assert (output_dir / "fitness_gen0004.png").exists()
    assert not (output_dir / "fitness_gen0001.png").exists()
    
    # Network visualizations should be at generations 0, 4
    assert (output_dir / "network_best_gen0000.png").exists()
    assert (output_dir / "network_best_gen0004.png").exists()
    assert not (output_dir / "network_best_gen0002.png").exists()
    
    # Test with empty networks
    empty_networks = NetworkBatch(
        node_indices=jnp.array([]).reshape(0, 10),
        connections=jnp.array([]).reshape(0, 20, 4),
        enabled=jnp.array([]).reshape(0, 20),
        node_types=jnp.array([]).reshape(0, 10),
        activation_fns=jnp.array([]).reshape(0, 10),
        num_inputs=2,
        num_outputs=1,
        max_nodes=10
    )
    # This should run without error but may not generate network visualizations
    try:
        hook(generation=10, networks=empty_networks, species_state=species_state, 
             fitnesses=jnp.array([]), best_fitness=0, best_idx=0)
    except Exception:
        pass  # Expected to fail on empty networks


def test_visualize_network_minimal(sample_networks: Dict[str, Network]):
    """Test visualization of a minimal network."""
    dot = visualize_network(network=sample_networks['minimal'], format='png')
    assert dot is not None
    assert "digraph" in dot.source

def test_visualize_network_xor(sample_networks: Dict[str, Network]):
    """Test visualization of XOR network."""
    dot = visualize_network(network=sample_networks['xor'], format='png')
    assert dot is not None
    assert "digraph" in dot.source

def test_plot_fitness_history_basic(tmp_path: Path):
    """Basic test of fitness history plotting."""
    save_path = tmp_path / "fitness.png"
    fig, ax = plot_fitness_history(
        fitness_history=jnp.array([0.1, 0.2, 0.3, 0.4, 0.5]),
        title="Test Fitness",
        save_path=str(save_path)
    )
    assert save_path.exists()
    plt.close(fig)

def test_visualization_hook_basic(tmp_path: Path, sample_networks: Dict[str, Network]):
    """Basic test of visualization hook."""
    output_dir = tmp_path / "viz_output"
    output_dir.mkdir()
    
    hook = create_visualization_hook(
        output_dir=str(output_dir),
        plot_interval=2,
        plot_species=True,
        format='png'
    )
    
    # Create a simple NetworkBatch from the sample network
    sample_net = sample_networks['xor']
    batch_size = 1
    networks = NetworkBatch(
        node_indices=jnp.arange(sample_net.max_nodes).reshape(batch_size, -1),
        connections=sample_net.connections.reshape(batch_size, -1, 4),
        enabled=sample_net.enabled.reshape(batch_size, -1),
        node_types=sample_net.node_types.reshape(batch_size, -1),
        activation_fns=sample_net.activation_fns.reshape(batch_size, -1),
        num_inputs=sample_net.num_inputs,
        num_outputs=sample_net.num_outputs,
        max_nodes=sample_net.max_nodes
    )
    
    # Create a simple species state
    species_state = SpeciesState(
        species_ids=jnp.array([0]),
        next_species_id=jnp.array(1, dtype=jnp.int32),
        active_mask=jnp.array([True]),
        member_masks=jnp.array([[True]]),
        representatives=jnp.zeros((1, 20, 4)),  # Match connections array size
        offspring_counts=jnp.array([1]),
        best_fitnesses=jnp.array([0.5]),
        last_improvements=jnp.array([0]),
        rng_key=jax.random.PRNGKey(0)
    )
    
    # Simulate evolution
    for gen in range(3):
        hook(
            generation=gen,
            networks=networks,
            species_state=species_state,
            fitnesses=jnp.array([0.5]),
            best_fitness=0.5,
            best_idx=0
        )
    
    # Should have saved some files
    assert (output_dir / "fitness_gen0000.png").exists()
    assert (output_dir / "fitness_gen0002.png").exists()