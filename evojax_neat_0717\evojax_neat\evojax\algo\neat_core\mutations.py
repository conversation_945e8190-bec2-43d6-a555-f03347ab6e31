from functools import partial
import chex
import jax
import jax.numpy as jnp

from .utils import sample_from_mask, find_first, check_probability
from .network import ActivationState, Network, NetworkBatch, update_depth
from .innovation import InnovationTracker
from .config.base import MutationConfig
from .constants import (
    NODE_BIAS,
    NODE_INPUT,
    NODE_OUTPUT,
    NODE_HIDDEN,
    NODE_UNUSED,
    EMPTY_SLOT
)

def shift_weights(
    key: chex.PRNGKey,
    net: Network,
    state: ActivationState,
    tracker: InnovationTracker,
    config: MutationConfig,
    use_high_mutation_rate: bool = False
) -> tuple[Network, ActivationState, InnovationTracker]:
    """Shifts the network's weights by a small value sampled from the normal distribution.

    Args:
        key (chex.PRNGKey): The random key used to sample from the normal distribution
        net (Network): The network to shift weights from
        state (ActivationState): Current activation state (unchanged by this mutation)
        tracker (InnovationTracker): Innovation tracker (unchanged by this mutation)
        use_high_mutation_rate (bool): If True, use a higher mutation rate (for testing)

    Returns:
        tuple:
            - Network: The network with updated weights
            - ActivationState: Unchanged activation state
            - InnovationTracker: Unchanged innovation tracker
    """
    # Split key for mutation decisions and normal distribution sampling
    should_mutate_key, epsilon_key = jax.random.split(key)

    # Extract weights and determine mutation parameters
    weights = net.connections[:, 2]
    mutation_rate = jax.lax.select(
        use_high_mutation_rate,
        0.9,  # High mutation rate for testing
        config.shift_weight_rate
    )

    # Determine which weights should be mutated
    should_mutate = jax.random.uniform(
        should_mutate_key, shape=weights.shape
    ) < mutation_rate

    # Sample shift values from normal distribution
    epsilons = jax.random.normal(
        epsilon_key, shape=weights.shape
    ) * config.weight_scale

    # Apply shifts to weights (vectorized operation)
    # Weight shifts are only applied to non-zero values (initialized weights)
    apply_shift = should_mutate & (weights != 0)
    shifted_weights = jnp.where(apply_shift, weights + epsilons, weights)

    # Create new connections array with updated weights
    new_connections = net.connections.at[:, 2].set(shifted_weights)

    # Create updated network
    updated_net = net.replace(connections=new_connections)

    # Return updated values (state and tracker unchanged by this mutation)
    return updated_net, state, tracker

def add_node(
    key: chex.PRNGKey,
    net: Network,
    state: ActivationState,
    tracker: InnovationTracker,
    config: MutationConfig,
    use_high_mutation_rate: bool = False
) -> tuple[Network, ActivationState, InnovationTracker]:
    """Inserts a new node in the network by splitting an existing connection.

    In practice, an existing `sender -> receiver` connection is replaced by:
    1. `sender -> new_node`
    2. `new_node -> receiver`
    3. `bias -> new_node` (bias connection)

    Args:
        key (chex.PRNGKey): Random key used to:
            ```
            * Sample the connection to split
            * Sample weights for new sub connections
            * Sample an activation function for the new node
            ```
        net (Network): The network to mutate
        state (ActivationState): Current activation state
        tracker (InnovationTracker): Tracker for consistent innovation numbers
        use_high_mutation_rate (bool): If True, always attempt mutation (for testing)

    Returns:
        tuple[Network, ActivationState, InnovationTracker]:
            - The mutated network with updated fields
            - The activation state (unchanged)
            - Updated innovation tracker
    """
    add_node_rate = config.add_node_rate

    # Split keys for different operations upfront
    probability_key, selection_key = jax.random.split(key, 2)

    # Step 1: Check if mutation should be attempted based on probability
    probability_check_passed = check_probability(
        probability_key,
        add_node_rate,
        high_rate=0.9,  # High mutation rate for testing
        use_high_rate=use_high_mutation_rate
    )

    # Step 2: Find a valid connection to split (only if probability check passes)
    def _find_valid_connection(key):
        """Find a valid connection to split.

        Returns:
            tuple: (is_valid_connection, conn_idx)
                is_valid_connection: Boolean indicating if the selected connection is valid
                conn_idx: Index of the randomly selected connection
        """
        # First, check if there are any connections with valid indices
        valid_indices_mask = jnp.logical_and(
            net.connections[:, 0] >= 0,  # Valid sender index
            net.connections[:, 1] >= 0   # Valid receiver index
        )

        # Check if we have any connections with valid indices
        has_connections = jnp.any(valid_indices_mask)

        # Randomly select a connection from those with valid indices
        conn_idx = jax.lax.cond(
            has_connections,
            lambda _: sample_from_mask(key, valid_indices_mask),
            lambda _: jnp.array(-1, dtype=jnp.int32),
            operand=None
        )

        # Now check if the selected connection is valid for the add_node mutation
        def _check_connection_validity(idx):
            # Get sender and receiver for the selected connection
            sender = net.connections[idx, 0].astype(jnp.int32)
            receiver = net.connections[idx, 1].astype(jnp.int32)

            # Get total number of nodes in the network
            total_nodes = net.node_types.shape[0]

            # Get node types (with bounds checking)
            sender_type = jnp.where(
                (sender >= 0) & (sender < total_nodes),
                net.node_types[sender],
                NODE_UNUSED
            )

            receiver_type = jnp.where(
                (receiver >= 0) & (receiver < total_nodes),
                net.node_types[receiver],
                NODE_UNUSED
            )

            # Check if sender is a valid type (input or hidden)
            valid_sender = (sender_type == NODE_INPUT) | (sender_type == NODE_HIDDEN)

            # Check if receiver is a valid type (output or hidden)
            valid_receiver = (receiver_type == NODE_OUTPUT) | (receiver_type == NODE_HIDDEN)

            # Check if the connection is enabled
            is_enabled = net.enabled[idx]

            # Connection is valid if all conditions are met
            return jnp.logical_and(
                jnp.logical_and(valid_sender, valid_receiver),
                is_enabled
            )

        # Check if the selected connection is valid
        is_valid_connection = jax.lax.cond(
            conn_idx >= 0,
            lambda idx: _check_connection_validity(idx),
            lambda _: jnp.array(False),
            operand=conn_idx  # Pass conn_idx
        )

        return is_valid_connection, conn_idx

    # Only perform selection if probability check passes
    selection_result = jax.lax.cond(
        probability_check_passed,
        lambda _: _find_valid_connection(selection_key),
        lambda _: (jnp.array(False), jnp.array(-1, dtype=jnp.int32)),
        operand=None
    )

    # Unpack selection result
    is_valid_connection, conn_idx = selection_result

    # Extract connection data only if we have a valid connection
    sender = jax.lax.cond(
        conn_idx >= 0,
        lambda _: net.connections[conn_idx, 0].astype(jnp.int32),
        lambda _: jnp.array(-1, dtype=jnp.int32),
        operand=None
    )

    receiver = jax.lax.cond(
        conn_idx >= 0,
        lambda _: net.connections[conn_idx, 1].astype(jnp.int32),
        lambda _: jnp.array(-1, dtype=jnp.int32),
        operand=None
    )

    weight = jax.lax.cond(
        conn_idx >= 0,
        lambda _: net.connections[conn_idx, 2].astype(jnp.float32),
        lambda _: jnp.array(0.0, dtype=jnp.float32),
        operand=None
    )

    # Step 3: Only proceed if both checks pass (probability and valid connection)
    should_attempt_mutation = jnp.logical_and(probability_check_passed, is_valid_connection)

    # Step 4: Apply the mutation if all checks pass
    def _apply_mutation(_):
        """Apply the add_node mutation."""
        # Create a mask for all empty connection slots
        empty_mask = jnp.logical_and(
            net.connections[:, 0] == EMPTY_SLOT,
            net.connections[:, 1] == EMPTY_SLOT
        )

        # Find first empty slot for new connection
        first_empty = find_first(empty_mask, default=EMPTY_SLOT)

        # For second slot, exclude the first slot from consideration
        second_mask = jax.lax.cond(
            first_empty != EMPTY_SLOT,
            lambda _: empty_mask.at[first_empty].set(False),
            lambda _: empty_mask,
            operand=None
        )
        second_empty = find_first(second_mask, default=EMPTY_SLOT)

        # For third slot, exclude both first and second slots
        third_mask = jax.lax.cond(
            second_empty != EMPTY_SLOT,
            lambda _: second_mask.at[second_empty].set(False),
            lambda _: second_mask,
            operand=None
        )
        third_empty = find_first(third_mask, default=EMPTY_SLOT)

        # Find unused node slot with safety check
        unused_mask = net.node_types == NODE_UNUSED  # Look for type 3 (unused) nodes
        new_node = jax.lax.cond(
            jnp.any(unused_mask),
            lambda _: find_first(unused_mask),
            lambda _: jnp.array(EMPTY_SLOT, dtype=jnp.int32),
            operand=None
        )

        # Check for valid slots and node
        valid_slots = jnp.logical_and(
            jnp.logical_and(first_empty != EMPTY_SLOT, second_empty != EMPTY_SLOT),
            third_empty != EMPTY_SLOT
        )
        valid_node = new_node != EMPTY_SLOT
        should_proceed = jnp.logical_and(valid_slots, valid_node)

        # Prepare mutation operation
        def do_split(_):
            # Disable the original connection that's being split
            new_enabled = net.enabled.at[conn_idx].set(False)

            # Weight Initialization
            new_weight1 = 1.0  # Use 1.0 as first connection weight (sender -> new_node)
            new_weight2 = weight  # Use original weight for second connection (new_node -> receiver)
            bias_weight = 1.0  # Bias weight for new nodes (project-specific design choice)

            # Get the next innovation number for first connection (sender -> new_node)
            innovation1, updated_tracker = tracker.get_innovation_id(sender, new_node)

            # Set the first new connection (sender -> new_node)
            new_connections = net.connections.at[first_empty, 0].set(sender)
            new_connections = new_connections.at[first_empty, 1].set(new_node)
            new_connections = new_connections.at[first_empty, 2].set(new_weight1)
            new_connections = new_connections.at[first_empty, 3].set(innovation1)
            new_enabled = new_enabled.at[first_empty].set(True)

            # Get the next innovation number for second connection (new_node -> receiver)
            innovation2, updated_tracker = updated_tracker.get_innovation_id(new_node, receiver)

            # Set the second new connection (new_node -> receiver)
            new_connections = new_connections.at[second_empty, 0].set(new_node)
            new_connections = new_connections.at[second_empty, 1].set(receiver)
            new_connections = new_connections.at[second_empty, 2].set(new_weight2)
            new_connections = new_connections.at[second_empty, 3].set(innovation2)
            new_enabled = new_enabled.at[second_empty].set(True)

            # Get the next innovation number for bias connection (bias -> new_node)
            bias_node = jnp.array(0, dtype=jnp.int32) # Bias node is typically node 0
            innovation3, updated_tracker = updated_tracker.get_innovation_id(bias_node, new_node)

            # Set the bias connection (bias -> new_node)
            new_connections = new_connections.at[third_empty, 0].set(bias_node)
            new_connections = new_connections.at[third_empty, 1].set(new_node)
            new_connections = new_connections.at[third_empty, 2].set(bias_weight)
            new_connections = new_connections.at[third_empty, 3].set(innovation3)
            new_enabled = new_enabled.at[third_empty].set(True)

            # Add the new node type (ensure it's set to hidden)
            new_node_types = net.node_types.at[new_node].set(NODE_HIDDEN)

            # Return updated network, state, and tracker
            updated_net = net.replace(
                connections=new_connections,
                enabled=new_enabled,
                node_types=new_node_types
            )

            # Mark depths as outdated since network topology has changed
            updated_state = state.replace(outdated_depths=True)

            return updated_net, updated_state, updated_tracker

        # Skip split if validation fails
        def skip_split(_):
            return net, state, tracker

        # Apply mutation if validation passes
        return jax.lax.cond(
            should_proceed,
            do_split,
            skip_split,
            operand=None
        )

    # Apply mutation only if all checks pass
    result = jax.lax.cond(
        should_attempt_mutation,
        _apply_mutation,
        lambda _: (net, state, tracker),  # Return original values (no mutation)
        operand=None
    )

    return result

def add_connection(
    key: chex.PRNGKey,
    net: Network,
    state: ActivationState,
    tracker: InnovationTracker,
    config: MutationConfig,
    use_high_mutation_rate: bool = False
) -> tuple[Network, ActivationState, InnovationTracker]:
    """Adds a new connection between two nodes in the network.

    Args:
        key (chex.PRNGKey): Random key used to:
            ```
            * Sample the sender and receiver nodes
            * Sample weight for the new connection
            ```
        net (Network): The network to mutate
        state (ActivationState): Current activation state
        tracker (InnovationTracker): Tracker for consistent innovation numbers
        use_high_mutation_rate (bool): If True, always attempt mutation (for testing)

    Returns:
        tuple[Network, ActivationState, InnovationTracker]:
            - The mutated network with updated fields
            - The updated activation state
            - Updated innovation tracker
    """
    add_connection_rate = config.add_connection_rate
    weight_scale = config.weight_scale

    # Split keys for different operations
    probability_key, selection_key, weight_key = jax.random.split(key, 3)

    # Step 1: Check if mutation should be attempted based on probability
    probability_check_passed = check_probability(
        probability_key,
        add_connection_rate,
        high_rate=0.9,
        use_high_rate=use_high_mutation_rate
    )

    # Step 2: Update activation state if outdated, regardless of probability check
    # Ensure node_depths shape matches network.max_nodes
    node_depths = state.node_depths
    target_shape = net.max_nodes
    if node_depths.shape[0] < target_shape:
        # Pad with zeros if too short
        node_depths = jnp.pad(node_depths, (0, target_shape - node_depths.shape[0]))
    elif node_depths.shape[0] > target_shape:
        # Truncate if too long
        node_depths = node_depths[:target_shape]
    updated_state = state.replace(node_depths=node_depths)

    # Update depths if outdated
    updated_state = jax.lax.cond(
        updated_state.outdated_depths,
        lambda _: update_depth(updated_state, net),
        lambda _: updated_state,
        operand=None
    )

    # Step 3: Find nodes to connect (only if probability check passes)
    def _select_valid_nodes(key, act_state):
        """Select valid sender and receiver nodes for a new connection.

        Following the backprop-neat-js approach, we:
        1. Select a random sender from input or hidden nodes
        2. Select a random receiver from hidden or output nodes
        3. Validate that the connection maintains the network's directed acyclic structure
        """
        sender_key, receiver_key = jax.random.split(key, num=2)

        # Connections can only be added from input and hidden nodes
        valid_senders = ((net.node_types == NODE_INPUT) |
                        (net.node_types == NODE_HIDDEN))
        selected_sender = sample_from_mask(sender_key, valid_senders)

        # Receivers can only be hidden or output nodes
        valid_receivers = ((net.node_types == NODE_HIDDEN) |
                          (net.node_types == NODE_OUTPUT))
        selected_receiver = sample_from_mask(receiver_key, valid_receivers)

        # Get the total number of nodes in the network (static shape)
        total_nodes = net.max_nodes

        # Create a padded array for node depths with zeros
        depths_padded = jnp.zeros(total_nodes, dtype=jnp.float32)

        # Pad the node_depths array to match total_nodes size
        # This is a static operation that doesn't use dynamic slicing
        padded_depths = jnp.pad(
            act_state.node_depths,
            [(0, max(0, total_nodes - act_state.node_depths.shape[0]))],
            mode='constant'
        )

        # Copy values from the padded array to depths_padded
        # This avoids dynamic slicing by using a fixed-size array
        depths_padded = padded_depths

        # Get depths of selected nodes
        sender_depth = depths_padded[selected_sender]
        receiver_depth = depths_padded[selected_receiver]

        # Validate that the connection maintains the directed acyclic structure
        # The receiver's depth must be greater than the sender's depth
        # This also ensures sender and receiver are different nodes
        is_valid_node_pair = receiver_depth > sender_depth

        # Return validation result and selected nodes
        return is_valid_node_pair, selected_sender, selected_receiver

    # Only perform selection if probability check passes
    selection_result = jax.lax.cond(
        probability_check_passed,
        lambda _: _select_valid_nodes(selection_key, updated_state),
        lambda _: (jnp.array(False), jnp.array(-1, dtype=jnp.int32),
                  jnp.array(-1, dtype=jnp.int32)),
        operand=None
    )

    # Unpack selection result
    is_valid_node_pair, sender, receiver = selection_result

    # Step 4: Only proceed if both checks pass
    should_attempt_mutation = jnp.logical_and(probability_check_passed, is_valid_node_pair)

    # Step 5: Apply the mutation if all checks pass
    def _apply_mutation(_):
        """Apply the add_connection mutation.

        Following the backprop-neat-js approach, we:
        1. Check if a connection already exists between the selected nodes
        2. If it exists but is disabled, re-enable it with a new random weight
        3. If it doesn't exist, create a new connection in an empty slot
        """        
        # First check if a connection already exists between sender and receiver
        connection_exists_mask = jnp.logical_and(
            net.connections[:, 0] == sender,
            net.connections[:, 1] == receiver
        )

        # Find the first matching connection (if any)
        existing_connection_idx = find_first(connection_exists_mask, default=EMPTY_SLOT)
        connection_exists = existing_connection_idx != EMPTY_SLOT

        # Check if the existing connection is disabled
        # Use jnp.where for a simpler conditional selection
        is_disabled = jnp.where(
            connection_exists,
            ~net.enabled[existing_connection_idx],
            jnp.array(False)
        )

        # Function to re-enable an existing connection
        def reenable_connection(_):
            # Generate a new random weight
            weight = jax.random.normal(weight_key) * weight_scale

            # Update the weight of the existing connection
            new_connections = net.connections.at[existing_connection_idx, 2].set(weight)

            # Enable the connection
            new_enabled = net.enabled.at[existing_connection_idx].set(True)

            # Create updated network
            updated_net = net.replace(
                connections=new_connections,
                enabled=new_enabled,
            )

            # No need to update depths since we're just re-enabling an existing connection
            return updated_net, updated_state, tracker

        # Function to create a new connection
        def create_new_connection(_):
            # Find empty slots (where both sender and receiver are EMPTY_SLOT)
            empty_mask = jnp.logical_and(
                net.connections[:, 0] == EMPTY_SLOT,
                net.connections[:, 1] == EMPTY_SLOT
            )
            first_empty = find_first(empty_mask, default=EMPTY_SLOT)

            # Check if we found an empty slot
            has_empty_slot = first_empty != EMPTY_SLOT

            def create_connection(_):
                # Use the provided key for weight generation
                weight = jax.random.normal(weight_key) * weight_scale

                # Get innovation number
                innovation, updated_tracker = tracker.get_innovation_id(sender, receiver)

                # Update connection fields
                new_connections = net.connections.at[first_empty, 0].set(sender)
                new_connections = new_connections.at[first_empty, 1].set(receiver)
                new_connections = new_connections.at[first_empty, 2].set(weight)
                new_connections = new_connections.at[first_empty, 3].set(innovation)

                # Enable the new connection
                new_enabled = net.enabled.at[first_empty].set(True)

                # Create updated network
                updated_net = net.replace(
                    connections=new_connections,
                    enabled=new_enabled,
                )

                # Mark depths as outdated since network topology has changed
                updated_state_with_outdated_depths = updated_state.replace(outdated_depths=True)

                return updated_net, updated_state_with_outdated_depths, updated_tracker

            # Return original values (no mutation applied)
            def skip_connection(_):
                return net, updated_state, tracker

            # Only create connection if we found an empty slot
            return jax.lax.cond(
                has_empty_slot,
                create_connection,
                skip_connection,
                operand=None
            )

        # If connection exists and is disabled, re-enable it
        # If connection doesn't exist, create a new one
        # If connection exists and is enabled, do nothing (skip)
        return jax.lax.cond(
            connection_exists,
            lambda _: jax.lax.cond(
                is_disabled,
                reenable_connection,
                lambda _: (net, updated_state, tracker),  # Connection already exists and is enabled
                operand=None
            ),
            create_new_connection,
            operand=None
        )

    # Apply mutation only if all checks pass
    result = jax.lax.cond(
        should_attempt_mutation,
        _apply_mutation,
        lambda _: (net, updated_state, tracker),  # Return original network with updated state
        operand=None
    )

    return result

def mutate(
    key: chex.PRNGKey,
    net: Network,
    state: ActivationState,
    tracker: InnovationTracker,
    config: MutationConfig,
    use_high_mutation_rate: bool = False
) -> tuple[Network, ActivationState, InnovationTracker]:
    """Apply all mutations to a network based on configured probabilities."""
    node_key, connection_key, w_shift_key = jax.random.split(key, 3)
    updated_net, updated_state, updated_tracker = shift_weights(w_shift_key, net, state, tracker, config, use_high_mutation_rate)
    updated_net, updated_state, updated_tracker = add_node(node_key, updated_net, updated_state, updated_tracker, config, use_high_mutation_rate)
    updated_net, updated_state, updated_tracker = add_connection(connection_key, updated_net, updated_state, updated_tracker, config, use_high_mutation_rate)
    return updated_net, updated_state, updated_tracker

@partial(jax.jit, static_argnames=['config', 'use_high_mutation_rate'])
def mutate_networks(
    key: chex.PRNGKey,
    networks: NetworkBatch,
    tracker: InnovationTracker,
    config: MutationConfig,
    use_high_mutation_rate: bool = False
) -> tuple[NetworkBatch, jnp.ndarray, InnovationTracker]:
    """
    JAX-compatible mutation of networks.

    Args:
        networks: Batched Network object (NetworkBatch Pytree) containing network configurations.
        key: Random key for reproducibility.
        tracker: Tracker for innovation numbers.
        config: MutationConfig object containing mutation rates and parameters.
        use_high_mutation_rate: If True, increases mutation attempt rates (for testing).

    Returns:
        tuple: (updated_networks, genome_indices, updated_tracker)
            updated_networks (NetworkBatch): The batch of updated Network Pytrees.
            genome_indices (jnp.ndarray): Indices of the processed genomes, shape [batch_size].
            updated_tracker (InnovationTracker): The updated innovation tracker state.
    """
    # Initialize node depths with proper structure
    max_nodes = networks.max_nodes
    num_inputs = networks.num_inputs
    num_outputs = networks.num_outputs

    # Initialize all depths to -1 (uninitialized)
    depths = jnp.full(max_nodes, -1, dtype=jnp.int32)
    # Set input nodes (including bias) to depth 0
    depths = depths.at[:num_inputs + 1].set(0)  # +1 for bias node
    # Set output nodes to max_nodes (treated as infinity)
    output_indices = jnp.arange(num_inputs + 1, num_inputs + num_outputs + 1)
    depths = depths.at[output_indices].set(max_nodes)

    # Create activation state - mark as outdated since depths need calculation
    init_state = ActivationState(
        node_depths=depths,
        outdated_depths=True
    )

    # Process networks sequentially
    def _process_network(carry, network_data):
        tracker, _ = carry
        network, network_key = network_data
        
        # Apply mutations
        updated_net, _, updated_tracker = mutate(
            network_key, network, init_state, tracker, config, use_high_mutation_rate)

        return (updated_tracker, None), updated_net

    # Split keys for each network
    batch_size = networks.connections.shape[0]
    keys = jax.random.split(key, batch_size)

    # Process all networks
    (updated_tracker, _), updated_networks = jax.lax.scan(
        _process_network,
        (tracker, None),
        (networks, keys)
    )

    # Create genome indices (just a range from 0 to batch_size-1)
    genome_indices = jnp.arange(batch_size, dtype=jnp.int32)

    return updated_networks, genome_indices, updated_tracker
