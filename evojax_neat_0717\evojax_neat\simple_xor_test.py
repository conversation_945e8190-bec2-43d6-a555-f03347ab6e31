"""
Simple test to check if networks are properly initialized
"""

import jax
import jax.numpy as jnp
import sys
import os

sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from evojax.algo.neat_core.config import XOR_CONFIG
from evojax.algo.neat_core.network import ActivationState, extract_single_network
from evojax.algo.neat_core import initialize_population, InnovationTracker, convert_genomes_to_networks

# XOR dataset
XOR_INPUTS = jnp.array([[-1.0, -1.0], [-1.0, 1.0], [1.0, -1.0], [1.0, 1.0]])
XOR_TARGETS = jnp.array([[-1.0], [1.0], [1.0], [-1.0]])

print("🔍 Simple XOR Network Test")
print("=" * 30)

# Initialize population
key = jax.random.PRNGKey(42)
tracker = InnovationTracker.create(XOR_CONFIG.network)

key, init_key = jax.random.split(key)
population, tracker = initialize_population(
    key=init_key,
    innovation_tracker=tracker,
    config=XOR_CONFIG
)

print(f"Population connections shape: {population.connections.shape}")
print(f"Population enabled shape: {population.enabled.shape}")

# Check first individual
print(f"\nFirst individual:")
print(f"  Connections: {population.connections[0]}")
print(f"  Enabled: {population.enabled[0]}")
print(f"  Enabled count: {jnp.sum(population.enabled[0])}")

# Convert to networks
networks, _ = convert_genomes_to_networks(
    connections=population.connections,
    enabled=population.enabled,
    config=XOR_CONFIG
)

# Test first network
single_network = extract_single_network(networks, 0)
print(f"\nFirst network:")
print(f"  Connections: {single_network.connections}")
print(f"  Enabled: {single_network.enabled}")
print(f"  Enabled count: {jnp.sum(single_network.enabled)}")
print(f"  Node types: {single_network.node_types}")

# Test forward pass with simple input
activation_state = ActivationState(
    node_depths=jnp.full(single_network.max_nodes, -1, dtype=jnp.int32),
    outdated_depths=True
)

test_input = jnp.array([[1.0, 1.0]])
try:
    output, _ = single_network.forward(test_input, activation_state)
    print(f"\nForward pass test:")
    print(f"  Input: {test_input}")
    print(f"  Output: {output}")
    print(f"  Output value: {output[0, 0]}")
except Exception as e:
    print(f"\nForward pass ERROR: {e}")

# Test all XOR inputs
print(f"\nXOR test:")
for i, input_pair in enumerate(XOR_INPUTS):
    try:
        output, _ = single_network.forward(input_pair.reshape(1, -1), activation_state)
        expected = XOR_TARGETS[i][0]
        actual = output[0, 0]
        print(f"  XOR({input_pair[0]:+.0f},{input_pair[1]:+.0f}): {actual:+.6f} (expected {expected:+.3f})")
    except Exception as e:
        print(f"  XOR({input_pair[0]:+.0f},{input_pair[1]:+.0f}): ERROR - {e}")

# Calculate MSE manually
try:
    outputs, _ = single_network.forward(XOR_INPUTS, activation_state)
    errors = outputs - XOR_TARGETS
    mse = jnp.mean(errors ** 2)
    fitness = 4.0 - mse
    print(f"\nManual calculation:")
    print(f"  Outputs: {outputs.flatten()}")
    print(f"  Targets: {XOR_TARGETS.flatten()}")
    print(f"  Errors: {errors.flatten()}")
    print(f"  MSE: {mse}")
    print(f"  Fitness: {fitness}")
except Exception as e:
    print(f"\nManual calculation ERROR: {e}")
