import jax
import jax.numpy as jnp
import chex
from typing import Callable, Any
import logging
import time

from .innovation import InnovationTracker
from .network import NetworkBatch
from .population import Population, initialize_population, convert_genomes_to_networks
from .species import SpeciesState, initialize_state, update_species
from .parent_selection_and_crossover import recombine
from .mutations import mutate_networks
from .backpropagation_and_fitness_evaluation import evaluate_networks
from .config.base import NEATConfig

logger = logging.getLogger(__name__)

def evolve_population(
    networks: NetworkBatch,
    species_state: SpeciesState,
    tracker: InnovationTracker,
    key: chex.PRNGKey,
    fitness_fn: Callable,
    inputs: jnp.ndarray,
    config: NEATConfig
) -> tuple[NetworkBatch, SpeciesState, InnovationTracker, jnp.ndarray, chex.PRNGKey]:
    """
    Evolve a batch of networks for one generation using the provided configuration.

    Args:
        networks: Current population (in NetworkBatch) to evolve.
        species_state: Current species state.
        tracker: Innovation tracker.
        key: JAX PRNGKey.
        fitness_fn: Fitness evaluation function.
        inputs: Input data for evaluation.
        config: NEATConfig object with all parameters.

    Returns:
        tuple of (new_networks, updated_species_state, updated_tracker, fitness_scores, new_key)
    """
    # Extract all config parameters at the beginning for consistency
    backprop_config = config.backprop
    species_config = config.species
    recombination_config = config.recombination
    mutation_config = config.mutation
    fitness_config = config.fitness

    # Extract specific values
    targets = config.targets

    # 1. Fitness evaluation (with/without backprop)
    key, eval_key = jax.random.split(key)
    networks, fitnesses = evaluate_networks(
        networks=networks,
        inputs=inputs,
        targets=targets,
        key=eval_key,
        fitness_fn=fitness_fn,
        backprop_config=backprop_config,
        fitness_config=fitness_config
    )

    # 2. Speciation
    species_state = update_species(
        state=species_state,
        connections=networks.connections,
        enabled=networks.enabled,
        fitness=fitnesses,
        config=species_config
    )

    # 3. Parent selection & crossover
    key, crossover_key = jax.random.split(key)
    new_connections, new_enabled = recombine(
        species_state=species_state,
        connections=networks.connections,
        enabled=networks.enabled,
        fitness=fitnesses,
        key=crossover_key,
        config=recombination_config
    )

    # 4. Convert genomes to networks (for mutation)
    networks, _ = convert_genomes_to_networks(
        connections=new_connections,
        enabled=new_enabled,
        config=config
    )

    # 5. Mutation (operates on networks)
    key, mutation_key = jax.random.split(key)
    mutated_networks, _, updated_tracker = mutate_networks(
        key=mutation_key,
        networks=networks,
        tracker=tracker,
        config=mutation_config
    )

    # Re-evaluate fitness for the final mutated networks (to ensure correctness)
    # This ensures returned fitness values correspond to the returned networks
    key, final_eval_key = jax.random.split(key)
    final_networks, final_fitnesses = evaluate_networks(
        networks=mutated_networks,
        inputs=inputs,
        targets=targets,
        key=final_eval_key,
        fitness_fn=fitness_fn,
        backprop_config=backprop_config,
        fitness_config=fitness_config
    )

    return final_networks, species_state, updated_tracker, final_fitnesses, key

def run_evolution(
    fitness_fn: Callable,
    inputs: jnp.ndarray,
    config: NEATConfig,
) -> tuple[Population, jnp.ndarray, jnp.ndarray, dict[str, Any]]:
    """
    Run the evolutionary process for a specified number of generations using the provided configuration.
    
    Args:
        fitness_fn: Function to evaluate fitness of networks.
        inputs: Input data for evaluation.
        config: NEATConfig object with all parameters.
            - seed: Random seed for reproducibility
            - max_generations: Maximum number of generations to run
            - log_progress: Whether to log progress
            - log_frequency: How often to log progress (in generations)
            - target_fitness: Optional target fitness to stop evolution
            - visualization_hook: Optional callback for visualization
            - population_size: Number of individuals in the population
            - max_generations: Maximum number of generations to run
            - network: NetworkConfig with network architecture parameters
            - species: SpeciesConfig with speciation parameters
    
    Returns:
        Tuple containing:
            - final_population: Population object with the final generation's networks
            - best_fitness: Fitness value of the best performing network
            - best_idx: Index of the best network in the population
            - metrics: Dictionary with evolution statistics (fitness history, species counts, etc.)
    """
    # Extract config parameters
    seed = config.seed
    max_generations = config.max_generations
    log_progress = config.log_progress
    log_frequency = config.log_frequency
    target_fitness = config.target_fitness
    visualization_hook = config.visualization_hook
    
    # Extract sub-configs
    population_config = config.population
    network_config = config.network
    species_config = config.species
    
    # Validate required parameters
    if not hasattr(population_config, 'population_size'):
        raise ValueError("Population config must specify population_size")
    if not hasattr(network_config, 'num_inputs') or not hasattr(network_config, 'num_outputs'):
        raise ValueError("Network config must specify num_inputs and num_outputs")
    
    # Note: evolve_population is not JIT compiled here because config objects are not hashable
    # Individual functions within evolve_population (like evaluate_networks) are JIT compiled internally
    
    # Initialize random key and tracker
    key = jax.random.PRNGKey(seed)
    tracker = InnovationTracker.create(network_config)

    # Initialize population
    key, init_key = jax.random.split(key)
    population, tracker = initialize_population(
        key=init_key,
        innovation_tracker=tracker,
        config=config
    )
    
    # Convert initial population to NetworkBatch for efficient evolution
    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=config
    )
    
    # Initialize species state with hierarchical config
    key, species_key = jax.random.split(key)
    species_state = initialize_state(
        connections=population.connections,
        key=species_key,
        config=species_config
    )

    # Initialize metrics tracking and best fitness
    best_fitness = -jnp.inf
    best_idx = jnp.array(0, dtype=jnp.int32)
    start_generation = 0
    
    if log_progress:
        logger.info(f"Initialized population with {population_config.population_size} genomes")
        logger.info(f"Network architecture: {network_config.num_inputs} inputs, {network_config.num_outputs} outputs")
    else:
        logger.debug(f"Initialized population with {population_config.population_size} genomes")

    # Prepare target fitness condition
    if target_fitness is None:
        has_target_fitness = False
        target_fitness_value = -jnp.inf
    else:
        has_target_fitness = True
        target_fitness_value = jnp.array(target_fitness)
    
    # Initialize metrics
    fitness_history = []
    species_counts = []
    
    # Main evolution loop
    done = False
    for generation in range(start_generation, max_generations):
        if done:
            break
            
        # Perform one generation of evolution
        start_time = time.time()
        networks, species_state, tracker, fitnesses, key = evolve_population(
            networks=networks,
            species_state=species_state,
            tracker=tracker,
            key=key,
            fitness_fn=fitness_fn,
            inputs=inputs,
            config=config
        )
        
        # Update best fitness and index
        current_best_idx = jnp.argmax(fitnesses)
        current_best_fitness = fitnesses[current_best_idx]
        
        if current_best_fitness > best_fitness:
            best_fitness = current_best_fitness
            best_idx = current_best_idx
            
            if log_progress:
                logger.info(f"New best fitness: {best_fitness}")
        
        # Check termination conditions
        if has_target_fitness and best_fitness >= target_fitness_value:
            if log_progress:
                logger.info(f"Target fitness {target_fitness_value} reached at generation {generation}")
            done = True
            
        # Check if population is empty or all connections are disabled
        if (networks.batch_size == 0 or 
            jnp.all(~networks.enabled) or 
            jnp.all(networks.connections[:, :, 0] == -1)):
            logger.warning(f"Population is empty or has no valid connections at generation {generation}")
            done = True
            
        # Update metrics
        fitness_history.append(float(current_best_fitness))
        # Compute species sizes from member masks
        species_sizes = jnp.sum(species_state.member_masks, axis=1)
        species_count = jnp.sum(species_sizes > 0)
        species_counts.append(int(species_count))
        
        # Log progress
        generation_time = time.time() - start_time
        if log_progress and (generation % log_frequency == 0 or generation == 0):
            logger.info(f"Generation {generation}: Best fitness = {best_fitness}, " 
                        f"Species: {species_count}, "
                        f"Time: {generation_time:.2f}s")
        else:
            logger.debug(f"Generation {generation}: Best fitness = {best_fitness}, " 
                         f"Species: {species_count}")
                   
        # Call visualization hook if provided
        if visualization_hook is not None:
            visualization_hook(
                generation=generation,
                networks=networks, 
                species_state=species_state,
                fitnesses=fitnesses,
                best_fitness=best_fitness,
                best_idx=best_idx
            )
    
    # Process metrics
    metrics = {
        'fitness_history': jnp.array(fitness_history),
        'species_counts': jnp.array(species_counts),
        'generations': len(fitness_history),
        'best_fitness': best_fitness,
        'best_idx': best_idx
    }
    
    # Convert final NetworkBatch back to Population for return
    # Calculate num_connections for each network in the batch
    num_connections = jnp.sum(networks.enabled, axis=1, dtype=jnp.int32)
    
    final_population = Population(
        connections=networks.connections,
        enabled=networks.enabled,
        num_connections=num_connections
    )

    return final_population, best_fitness, best_idx, metrics