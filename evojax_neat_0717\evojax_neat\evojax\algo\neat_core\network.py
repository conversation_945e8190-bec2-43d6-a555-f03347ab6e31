import jax
import jax.numpy as jnp
from flax import struct

from .activation_fns import get_activation_fn
from .config.base import NetworkConfig
from .constants import (
    NODE_BIAS,
    NODE_INPUT,
    NODE_OUTPUT,
    NODE_HIDDEN,
    NODE_UNUSED,
    ACT_FN_MAP,
    ACTIVATION_RELU,
    ACTIVATION_SIGMOID,
    ACTIVATION_IDENTITY
)

# ---------------------------
# Utility dataclasses
# ---------------------------

@struct.dataclass
class ActivationState:
    """State for tracking node activations and depth information."""
    node_depths: jnp.ndarray  # shape: (max_nodes,)
    outdated_depths: bool = True

    def __repr__(self) -> str:
        return f"ActivationState(node_depths={self.node_depths}, outdated_depths={self.outdated_depths})"

@struct.dataclass
class DepthState:
    """State for tracking depth calculation progress."""
    depths: jnp.ndarray  # shape: (max_nodes,)
    has_changed: bool = True

# ---------------------------
# Main Network class
# ---------------------------

@struct.dataclass
class Network(struct.PyTreeNode):
    """
    Represents the neural network structure.
    This version is designed for NEAT operations like mutation and crossover,
    and for evaluation when combined with ActivationState.

    Attributes:
        num_inputs (int): Number of input nodes. (Static)
        num_outputs (int): Number of output nodes. (Static)
        max_nodes (int): Maximum number of nodes allowed in the network. (Static)

        node_types (jnp.ndarray): Type of each node (input, output, hidden, bias, unused).
                                  Shape: (max_nodes,)
        connections (jnp.ndarray): Stores connection data: [sender, receiver, weight, innovation_id].
                                   Shape: (DEFAULT_MAX_CONNECTIONS, 4)
        enabled (jnp.ndarray): Boolean mask for active connections.
                               Shape: (DEFAULT_MAX_CONNECTIONS,)
        activation_fns (jnp.ndarray): Activation function index per node.
                                      Shape: (max_nodes,)
    """
    # Dynamic parts (pytree leaves, will be traced by JAX)
    node_types: jnp.ndarray    # shape: (max_nodes,)
    connections: jnp.ndarray   # shape: (DEFAULT_MAX_CONNECTIONS, 4)
    enabled: jnp.ndarray       # shape: (DEFAULT_MAX_CONNECTIONS,)
    activation_fns: jnp.ndarray  # shape: (max_nodes,)

    # Static configuration (shape-defining, not part of the pytree for differentiation)
    num_inputs: int = struct.field(pytree_node=False, default=2)  # Default values match NetworkConfig
    num_outputs: int = struct.field(pytree_node=False, default=1)
    max_nodes: int = struct.field(pytree_node=False, default=100)
    max_depth: int = struct.field(pytree_node=False, default=100)  # Same as max_nodes by default
    
    # ---- Properties ----
    @property
    def num_connections(self) -> int:
        """Returns the number of connections in the network."""
        return self.connections.shape[0]

    @property
    def senders(self) -> jnp.ndarray:
        """Returns the sender node indices for all connections.
        
        Returns:
            jnp.ndarray: Array of sender indices, shape (num_connections,)
        """
        return self.connections[:, 0]

    @property
    def receivers(self) -> jnp.ndarray:
        """Returns the receiver node indices for all connections.
        
        Returns:
            jnp.ndarray: Array of receiver indices, shape (num_connections,)
        """
        return self.connections[:, 1]

    @property
    def weights(self) -> jnp.ndarray:
        """Returns the weights for all connections.
        
        Returns:
            jnp.ndarray: Array of connection weights, shape (num_connections,)
        """
        return self.connections[:, 2]

    # ---- Forward Pass ----
    def forward(
        self, 
        inputs: jnp.ndarray, 
        activation_state: ActivationState,
        return_all_values: bool = False
    ) -> tuple[jnp.ndarray, ActivationState] | tuple[jnp.ndarray, ActivationState, jnp.ndarray]:
        """Perform forward pass through the network.
        
        Args:
            inputs: Input values for the network. Shape: (outer_shape..., num_inputs)
            activation_state: Current state containing node depths and update flag
            return_all_values: If True, also return all node values
            
        Returns:
            If return_all_values is False:
                tuple[outputs, activation_state] where:
                    outputs: Network outputs. Shape: (outer_shape..., num_outputs)
                    activation_state: Updated activation state
            If return_all_values is True:
                tuple[outputs, activation_state, all_values] where:
                    outputs: Network outputs. Shape: (outer_shape..., num_outputs)
                    activation_state: Updated activation state
                    all_values: All node values. Shape: (outer_shape..., max_nodes)
        """
        # Initialize network state
        outer_shape = inputs.shape[:-1]
        max_nodes = self.max_nodes
        node_values = jnp.zeros(outer_shape + (max_nodes,), dtype=inputs.dtype)  # concatenate the tuples
        node_values = node_values.at[..., 0].set(1.0)  # bias node always 1.0
        node_values = node_values.at[..., 1:self.num_inputs+1].set(inputs)
    
        # Update connection metadata
        senders = self.senders.astype(jnp.int32)
        receivers = self.receivers.astype(jnp.int32)
        weights = self.weights
        connection_mask = (senders >= 0) & (receivers >= 0) & self.enabled
    
        # Refresh depth calculations if needed
        activation_state = activation_state.replace(
            node_depths=activation_state.node_depths.astype(jnp.int32)
        )
        activation_state = jax.lax.cond(
            activation_state.outdated_depths,
            lambda state: update_depth(state, self),
            lambda state: state,
            activation_state
        )
        node_depths = activation_state.node_depths
        # Prepare depth processing parameters
        max_depth = jnp.max(node_depths)
    
        # Process all relevant depth levels
        def process_depth_level(node_values: jnp.ndarray, depth_idx: int) -> tuple[jnp.ndarray, None]:
            """Process all nodes at a specific depth level.
            
            Args:
                node_values: Current state of all node values
                depth_idx: Current depth level to process
                
            Returns:
                tuple[updated_values, None] where:
                    updated_values: Node values after processing this depth level
                    None: Placeholder for scan's carry value
            """
            # Early stopping condition: if no nodes at this depth, return unchanged
            has_nodes_at_depth = jnp.any(node_depths == depth_idx)
            
            def process_nodes(node_values):
                # 1. Aggregate inputs to current depth nodes
                depth_connections = (
                    (node_depths[receivers] == depth_idx) &
                    (node_depths[senders] < depth_idx) &
                    connection_mask
                )
                # Only update receivers for active connections at this depth
                updates = node_values[..., senders] * weights * depth_connections
                # Use scatter_add to update receiver nodes
                new_state = node_values.at[..., receivers].add(updates)

                # Apply activation functions to all nodes at current depth
                def apply_activation_to_node(node_idx, state):
                    # Only apply activation if this node is at the current depth AND not unused
                    is_at_depth = node_depths[node_idx] == depth_idx
                    is_not_unused = self.node_types[node_idx] != NODE_UNUSED  # Defensive check
                    act_idx = self.activation_fns[node_idx]
                    
                    # Get current value and apply activation if needed
                    current_value = state[..., node_idx]
                    # Update only if this node is at current depth, not unused, and has valid activation
                    should_update = is_at_depth & is_not_unused & (act_idx >= 0) & (act_idx < len(ACT_FN_MAP))
                    new_value = jnp.where(should_update, get_activation_fn(act_idx, current_value), current_value)
                    
                    return state.at[..., node_idx].set(new_value)
                
                # Process all nodes (JAX will optimize away unnecessary operations)
                activated_state = jax.lax.fori_loop(0, self.max_nodes, apply_activation_to_node, new_state)

                return activated_state, None
            
            def skip_processing(node_values):
                return node_values, None
            
            return jax.lax.cond(
                has_nodes_at_depth,
                process_nodes,
                skip_processing,
                node_values
            )

        # Create a sequence of depth indices from 0 to actual max depth
        depth_indices = jnp.arange(self.max_depth)
    
        # Use scan with the fixed depth range
        final_values, _ = jax.lax.scan(
            process_depth_level,
            node_values,
            depth_indices
        )
    
        # Extract outputs using NEAT convention: outputs are from num_inputs+1 to num_inputs+num_outputs
        output_indices = jnp.arange(self.num_inputs + 1, self.num_inputs + self.num_outputs + 1)
        outputs = final_values[..., output_indices]
        
        if return_all_values:
            return outputs, activation_state, final_values
        return outputs, activation_state

# NetworkBatch represents any structure that can contain Network objects

@struct.dataclass
class NetworkBatch(struct.PyTreeNode):
    """
    A Pytree representing a batch of Network structures.

    The JAX array attributes (node_types, connections, enabled,
    activation_fns) are expected to have a leading batch dimension.
    For example, if a single Network has `connections` of shape
    (max_connections, 4), then in a NetworkBatch, `connections` would have
    shape (batch_size, max_connections, 4).

    Static configuration parameters (num_inputs, num_outputs, max_nodes)
    are shared across all networks in the batch and are not batched.
    """
    # Batched dynamic parts (pytree leaves)
    # These arrays will have a leading batch_size dimension.
    node_indices: jnp.ndarray   # shape: (batch_size, max_nodes)
    node_types: jnp.ndarray     # shape: (batch_size, max_nodes)
    connections: jnp.ndarray    # shape: (batch_size, DEFAULT_MAX_CONNECTIONS, 4)
    enabled: jnp.ndarray        # shape: (batch_size, DEFAULT_MAX_CONNECTIONS)
    activation_fns: jnp.ndarray # shape: (batch_size, max_nodes)

    # Static configuration (shape-defining, shared across the batch)
    num_inputs: int = struct.field(pytree_node=False)
    num_outputs: int = struct.field(pytree_node=False)
    max_nodes: int = struct.field(pytree_node=False)

    @property
    def senders(self) -> jnp.ndarray:
        """Returns the sender node indices for all connections in the batch.
        
        Returns:
            jnp.ndarray: Array of sender indices, shape (batch_size, num_connections)
        """
        return self.connections[..., 0]
        
    @property
    def receivers(self) -> jnp.ndarray:
        """Returns the receiver node indices for all connections in the batch.
        
        Returns:
            jnp.ndarray: Array of receiver indices, shape (batch_size, num_connections)
        """
        return self.connections[..., 1]
        
    @property
    def weights(self) -> jnp.ndarray:
        """Returns the weights for all connections in the batch.
        
        Returns:
            jnp.ndarray: Array of connection weights, shape (batch_size, num_connections)
        """
        return self.connections[..., 2]
    
    @property
    def batch_size(self) -> int:
        """Returns the batch size (number of networks in the batch).
        
        Returns:
            int: Number of networks in this batch
        """
        return self.node_types.shape[0]
        

        

# ---------------------------
# Depth Calculation Utility
# ---------------------------

def update_depth(
    activation_state: ActivationState,
    network: Network
) -> ActivationState:
    """Calculate node depths through enabled connections using dynamic programming.
    
    Args:
        activation_state: Current state containing node depths and update flag
        network: Network structure to calculate depths for
        
    Returns:
        Updated activation state with new node depths
        
    Note:
        - Maximum supported network size is 2^31-1 nodes (int32 limit)
        - Cycles are handled by limiting iterations to max_nodes
        - Disconnected nodes will have depth -1
        - Performance is O(max_nodes * num_connections)
    """
    def compute_depths(state):
        max_nodes = network.max_nodes
        
        # Initialize depths
        is_input_node = (jnp.arange(max_nodes) < network.num_inputs + 1)
        initial_depths = jnp.where(is_input_node, 0, -1)
        
        # Validate and prepare connections
        valid_senders = network.senders >= 0
        valid_receivers = network.receivers >= 0
        enabled_connections = network.enabled & valid_senders & valid_receivers
        
        # Ensure indices fit in int32
        senders = network.senders.astype(jnp.int32)
        receivers = network.receivers.astype(jnp.int32)
        
        def propagate_depths_once(depth_state: DepthState) -> DepthState:
            current_depths = depth_state.depths
            # Combined operations to reduce memory usage
            new_depths = current_depths.at[receivers].max(
                jnp.where(enabled_connections, 
                         jnp.take(current_depths, senders) + 1, 
                         -1)
            )
            new_depths = jnp.where(is_input_node, 0, new_depths)
            has_changed = jnp.any(new_depths != current_depths)
            return DepthState(depths=new_depths, has_changed=has_changed)
        
        # Continue until no changes or max iterations
        def body(_, depth_state):
            new_state = propagate_depths_once(depth_state)
            return jax.lax.cond(
                new_state.has_changed,
                lambda _: new_state,
                lambda _: depth_state,
                operand=None
            )
        
        final_state = jax.lax.fori_loop(
            0,
            max_nodes,
            body,
            DepthState(depths=initial_depths, has_changed=True)
        )
        
        return state.replace(
            node_depths=final_state.depths.astype(jnp.int32),
            outdated_depths=False
        )
    
    return jax.lax.cond(
        activation_state.outdated_depths,
        compute_depths,
        lambda state: state,
        activation_state
    )

def init_network(
    connections: jnp.ndarray,
    enabled: jnp.ndarray,
    config: NetworkConfig
) -> Network:
    """Initialize a Network from connection data.
    
    Args:
        connections: Array of [num_connections, 4] containing
                    (sender, receiver, weight, innovation_id)
        enabled: Boolean array indicating active connections
        config: NetworkConfig instance containing network parameters
    
    Returns:
        Configured Network instance with proper node typing
    """
    # Extract parameters from config
    num_inputs = config.num_inputs
    num_outputs = config.num_outputs
    max_nodes = config.max_nodes
    hidden_activation = config.hidden_activation
    output_activation = config.output_activation
    
    # Convert connection indices to fixed precision
    senders = connections[:, 0].astype(jnp.int32)
    receivers = connections[:, 1].astype(jnp.int32)
    
    # Create node usage mask (True if node appears in any connection)
    used_mask = (
        jnp.zeros(max_nodes, dtype=jnp.bool_)
        .at[senders].set(True)
        .at[receivers].set(True)
    )

    # Initialize node types
    # num_nodes = num_inputs + num_outputs + 1  # +1 for bias node
    node_types = jnp.full(max_nodes, NODE_UNUSED, dtype=jnp.int32)
    
    # Set node types
    node_types = node_types.at[0].set(NODE_BIAS)  # Bias node
    node_types = node_types.at[1:num_inputs+1].set(NODE_INPUT)  # Input nodes
    node_types = node_types.at[num_inputs+1:num_inputs+num_outputs+1].set(NODE_OUTPUT)  # Output nodes (indices after inputs)
    
    # Set hidden nodes (used nodes that aren't input/output/bias)
    hidden_start = num_inputs + num_outputs + 1
    hidden_nodes = (jnp.arange(max_nodes) >= hidden_start) & used_mask
    node_types = jnp.where(hidden_nodes, NODE_HIDDEN, node_types)
    
    # Set activation functions based on config - match constants.py values
    hidden_act = ACT_FN_MAP.get(hidden_activation, ACTIVATION_RELU)  # Default to relu
    output_act = ACT_FN_MAP.get(output_activation, ACTIVATION_SIGMOID)  # Default to sigmoid
    
    # Initialize activation functions
    activation_fns = jnp.full(max_nodes, -1, dtype=jnp.int32)  # All nodes start with -1 (no activation)
    activation_fns = activation_fns.at[0].set(ACTIVATION_IDENTITY)  # Identity for bias 
    activation_fns = activation_fns.at[1:num_inputs+1].set(ACTIVATION_IDENTITY)  # Input nodes use identity
    activation_fns = activation_fns.at[num_inputs+1:num_inputs+num_outputs+1].set(output_act)  # Output nodes
    
    # Set activation functions for hidden nodes
    hidden_nodes = (jnp.arange(max_nodes) >= hidden_start) & used_mask
    activation_fns = jnp.where(hidden_nodes, hidden_act, activation_fns)  # Hidden nodes use hidden_act

    return Network(
        connections=connections,
        enabled=enabled,
        num_inputs=num_inputs,
        num_outputs=num_outputs,
        node_types=node_types,
        activation_fns=activation_fns,
        max_nodes=max_nodes,
        max_depth=max_nodes  # Set max_depth equal to max_nodes by default
    )


# ---------------------------
# Network/NetworkBatch Conversion Utilities
# ---------------------------

def extract_single_network(networks: NetworkBatch, idx: int) -> Network:
    """Extract a single Network from a NetworkBatch at the given index.
    
    Args:
        networks: NetworkBatch containing multiple networks
        idx: Index of the network to extract
        
    Returns:
        Individual Network object at the specified index
        
    Example:
        >>> batch = NetworkBatch(...)  # batch_size = 10
        >>> single_net = extract_single_network(batch, 3)  # Extract 4th network
    """
    return Network(
        num_inputs=networks.num_inputs,
        num_outputs=networks.num_outputs,
        max_nodes=networks.max_nodes,
        node_types=networks.node_types[idx],
        connections=networks.connections[idx],
        enabled=networks.enabled[idx],
        activation_fns=networks.activation_fns[idx]
    )


def reconstruct_network_batch(individual_networks: Network) -> NetworkBatch:
    """Reconstruct a NetworkBatch from vmapped individual networks.
    
    This function is useful when you have individual Network objects that were
    processed via jax.vmap and need to convert them back to a NetworkBatch.
    
    Args:
        individual_networks: Network object where all fields have been vmapped
                           (i.e., all arrays have a leading batch dimension)
        
    Returns:
        NetworkBatch object with proper batch structure
        
    Example:
        >>> def process_network(net): ...
        >>> batch_networks = jax.vmap(process_network)(individual_networks)
        >>> batch = reconstruct_network_batch(batch_networks)
    """
    # When vmapped, individual_networks will have a batch dimension in all arrays
    batch_size = individual_networks.node_types.shape[0]
    node_indices = jnp.tile(jnp.arange(individual_networks.max_nodes)[None, :], (batch_size, 1))
    
    return NetworkBatch(
        node_indices=node_indices,
        node_types=individual_networks.node_types,
        connections=individual_networks.connections,
        enabled=individual_networks.enabled,
        activation_fns=individual_networks.activation_fns,
        num_inputs=individual_networks.num_inputs,
        num_outputs=individual_networks.num_outputs,
        max_nodes=individual_networks.max_nodes
    )