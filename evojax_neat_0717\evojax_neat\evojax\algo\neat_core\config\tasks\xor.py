"""
XOR task configuration for the NEAT algorithm.
"""
import jax.numpy as jnp
from ..base import (
    PopulationConfig,
    NetworkConfig,
    SpeciesConfig,
    RecombinationConfig,
    MutationConfig,
    FitnessConfig,
    BackpropConfig,
    NEATConfig
)

# XOR dataset
XOR_INPUTS = jnp.array([[-1.0, -1.0], [-1.0, 1.0], [1.0, -1.0], [1.0, 1.0]])
XOR_TARGETS = jnp.array([[-1.0], [1.0], [1.0], [-1.0]])

# XOR configuration - ultra-aggressive for 3-5 generation breakthrough
XOR_CONFIG = NEATConfig(
    # Core evolution parameters
    seed=42,
    max_generations=6,    # Ultra-fast convergence
    target_fitness=4.0,   # Perfect target
    log_progress=True,
    log_frequency=1,      # Log every generation
    
    # Task-specific data
    inputs=XOR_INPUTS,
    targets=XOR_TARGETS,

    # Population
    population=PopulationConfig(
        population_size=100,  # Reduced from 300
        weight_init_std=3.0,  # Large initial weights
        weight_init_mean=0.0
    ),

    # Network
    network=NetworkConfig(
        num_inputs=2,
        num_outputs=1,
        max_nodes=20,
        max_connections=50,
        activation_fn="tanh",
        output_activation="tanh",
        hidden_activation="tanh"
    ),

    # Species - ~5-6 species
    species=SpeciesConfig(
        max_species=8,               # Limit to ~5-6 species
        gene_coefficient=1.0,
        weight_coefficient=0.4,
        compatibility_threshold=2.5, # Tighter for fewer species
        stagnation_threshold=8,
        rank_strategy=1
    ),

    # Recombination
    recombination=RecombinationConfig(
        tournament_size=3,
        parent1_gene_rate=0.5,
        elite_ratio=0.02,    # Keep only the very best
        cull_ratio=0.8       # Very aggressive selection
    ),

    # Mutation - ultra-aggressive
    mutation=MutationConfig(
        add_node_rate=0.3,      # Very high structural mutation
        add_connection_rate=0.7, # Very high connection mutation
        shift_weight_rate=0.99,  # Almost always mutate weights
        weight_scale=6.0         # Large weight changes
    ),

    # Fitness - no penalties for maximum performance
    fitness=FitnessConfig(
        connection_cost=0.0,
        node_cost=0.0
    ),

    # Backprop - critical for fast convergence
    backprop=BackpropConfig(
        enabled=True,
        rounds=30,           # Many backprop rounds
        learning_rate=0.08,  # High learning rate
        gradient_clip=5.0,
        max_errors=50,
        num_epochs=8,
        batch_size=4,        # Perfect for XOR
        max_weight=25.0      # Allow large weights
    )
)
