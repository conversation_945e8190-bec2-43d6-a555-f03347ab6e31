"""
Task-specific configurations for Hybrid NEAT.

This module contains optimized configuration presets for different tasks:
- XOR: Classic XOR problem (2 inputs, 1 output, non-linear)
- Circle: Circle classification (2D points, circular boundary)
- Spiral: Spiral classification (2D points, complex spiral boundary)

Each task module provides:
- Individual component configurations (network, population, etc.)
- Complete task configuration
- Performance-optimized variants
"""

# Task configurations are imported in the parent __init__.py
# This allows for clean imports like: from config.tasks.xor import XOR_CONFIG
