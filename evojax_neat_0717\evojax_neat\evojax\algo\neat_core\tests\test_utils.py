import pytest
import jax.numpy as jnp
import chex
import jax
from ..utils import find_first, check_probability, sample_from_mask

# Tests for find_first function
def test_find_first_basic():
    """Test basic functionality of find_first."""
    mask = jnp.array([<PERSON>als<PERSON>, <PERSON>als<PERSON>, <PERSON>, <PERSON>als<PERSON>, <PERSON>])
    result = find_first(mask)
    assert result == 2  # First True is at index 2

def test_find_first_no_match():
    """Test find_first when no True values exist."""
    mask = jnp.array([False, False, False])
    result = find_first(mask)
    assert result == -1  # Default value when no match

def test_find_first_custom_default():
    """Test find_first with custom default value."""
    mask = jnp.array([<PERSON>als<PERSON>, <PERSON>als<PERSON>, <PERSON>als<PERSON>])
    result = find_first(mask, default=-999)
    assert result == -999  # Custom default value

def test_find_first_jit():
    """Test that find_first works with JAX JIT."""
    @jax.jit
    def jitted_find_first(mask, default=-1):
        return find_first(mask, default)

    # Test with a match
    mask1 = jnp.array([<PERSON>als<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>])
    result1 = jitted_find_first(mask1)
    assert result1 == 1

    # Test without a match
    mask2 = jnp.array([False, False, False])
    result2 = jitted_find_first(mask2)
    assert result2 == -1

    # Test with custom default
    result3 = jitted_find_first(mask2, default=-100)
    assert result3 == -100

# Tests for check_probability function
def test_check_probability_basic():
    """Test basic functionality of check_probability."""
    key = jax.random.PRNGKey(0)

    # With rate=0, should always return False
    result_zero = check_probability(key, rate=0.0)
    assert result_zero == False

    # With rate=1, should always return True
    result_one = check_probability(key, rate=1.0)
    assert result_one == True

    # With rate=0.5, should be probabilistic
    # We can't assert exact values due to randomness, but we can run it multiple times
    results = []
    for i in range(100):
        subkey = jax.random.fold_in(key, i)
        results.append(check_probability(subkey, rate=0.5))

    # Should have a mix of True and False values
    assert jnp.any(jnp.array(results))
    assert jnp.any(~jnp.array(results))

def test_check_probability_high_rate():
    """Test check_probability with high rate option."""
    key = jax.random.PRNGKey(42)

    # With use_high_rate=True, should use high_rate instead of rate
    result = check_probability(key, rate=0.0, high_rate=1.0, use_high_rate=True)
    assert result == True

    # With use_high_rate=False, should use rate
    result = check_probability(key, rate=0.0, high_rate=1.0, use_high_rate=False)
    assert result == False

def test_check_probability_jit():
    """Test that check_probability works with JAX JIT."""
    @jax.jit
    def jitted_check(key, rate, high_rate=0.9, use_high_rate=False):
        return check_probability(key, rate, high_rate, use_high_rate)

    key = jax.random.PRNGKey(123)

    # Test with rate=0 (should always be False)
    result1 = jitted_check(key, 0.0)
    assert result1 == False

    # Test with rate=1 (should always be True)
    result2 = jitted_check(key, 1.0)
    assert result2 == True

    # Test with high_rate
    result3 = jitted_check(key, 0.0, high_rate=1.0, use_high_rate=True)
    assert result3 == True

# Tests for sample_from_mask function
def test_sample_from_mask_basic():
    """Test basic sampling from boolean mask."""
    key = jax.random.PRNGKey(0)
    mask = jnp.array([True, False, True, False])

    # Run multiple times to check randomness
    for _ in range(10):
        result = sample_from_mask(key, mask)
        assert result in [0, 2]  # Only valid indices
        key = jax.random.split(key)[0]

def test_sample_from_mask_all_true():
    """Test sampling when all values are valid."""
    key = jax.random.PRNGKey(42)
    mask = jnp.ones(5, dtype=bool)

    result = sample_from_mask(key, mask)
    assert 0 <= result < 5

def test_sample_from_mask_single_valid():
    """Test sampling when only one valid option exists."""
    key = jax.random.PRNGKey(123)
    mask = jnp.array([False, True, False, False])

    result = sample_from_mask(key, mask)
    assert result == 1

def test_sample_from_mask_with_indices():
    """Test sampling with custom indices array."""
    key = jax.random.PRNGKey(456)
    mask = jnp.array([True, False, True])
    indices = jnp.array([10, 20, 30])

    # Sample from the mask and map to indices
    result = sample_from_mask(key, mask, indices)

    # Result should be one of the indices at positions where mask is True
    assert result in [indices[0], indices[2]]  # indices[0]=10, indices[2]=30
    assert result in [10, 30]  # Explicitly check values

def test_sample_from_mask_jit():
    """Test that sample_from_mask works with JAX JIT."""
    @jax.jit
    def jitted_sample(key, mask, indices=None):
        return sample_from_mask(key, mask, indices)

    key = jax.random.PRNGKey(789)
    mask = jnp.array([True, False, True])

    # Test without indices
    result1 = jitted_sample(key, mask)
    assert result1 in [0, 2]

    # Test with indices
    indices = jnp.array([10, 20, 30])
    result2 = jitted_sample(key, mask, indices)
    assert result2 in [10, 30]

    # Test with empty mask
    empty_mask = jnp.zeros(3, dtype=bool)
    result3 = jitted_sample(key, empty_mask)
    assert result3 == -1

def test_sample_from_mask_empty():
    """Test behavior with empty mask (all False)."""
    key = jax.random.PRNGKey(999)
    mask = jnp.zeros(5, dtype=bool)

    # Updated implementation returns -1 for empty masks
    result = sample_from_mask(key, mask)
    assert result == -1

def test_sample_from_mask_empty_with_indices():
    """Test behavior with empty mask and indices."""
    key = jax.random.PRNGKey(111)
    mask = jnp.zeros(3, dtype=bool)
    indices = jnp.array([100, 200, 300])

    # Should still return -1 for empty masks, even with indices
    result = sample_from_mask(key, mask, indices)
    assert result == -1

def test_sample_from_mask_jit_with_static_indices():
    """Test JIT with static vs dynamic indices."""
    # Function with static indices (known at compile time)
    @jax.jit
    def jitted_sample_static(key, mask):
        indices = jnp.array([10, 20, 30])
        return sample_from_mask(key, mask, indices)

    # Function with dynamic indices (not known at compile time)
    @jax.jit
    def jitted_sample_dynamic(key, mask, indices):
        return sample_from_mask(key, mask, indices)

    key = jax.random.PRNGKey(222)
    mask = jnp.array([True, False, True])
    indices = jnp.array([10, 20, 30])

    # Both should work correctly
    result_static = jitted_sample_static(key, mask)
    result_dynamic = jitted_sample_dynamic(key, mask, indices)

    assert result_static in [10, 30]
    assert result_dynamic in [10, 30]

    # Test with empty mask
    empty_mask = jnp.zeros(3, dtype=bool)
    result_static_empty = jitted_sample_static(key, empty_mask)
    result_dynamic_empty = jitted_sample_dynamic(key, empty_mask, indices)

    assert result_static_empty == -1
    assert result_dynamic_empty == -1



if __name__ == "__main__":
    # Run all tests
    print("Running tests...")

    # Find first tests
    test_find_first_basic()
    test_find_first_no_match()
    test_find_first_custom_default()
    test_find_first_jit()

    # Check probability tests
    test_check_probability_basic()
    test_check_probability_high_rate()
    test_check_probability_jit()



    # Sample from mask tests
    test_sample_from_mask_basic()
    test_sample_from_mask_all_true()
    test_sample_from_mask_single_valid()
    test_sample_from_mask_with_indices()
    test_sample_from_mask_jit()
    test_sample_from_mask_empty()
    test_sample_from_mask_empty_with_indices()
    test_sample_from_mask_jit_with_static_indices()

    print("All tests passed!")
