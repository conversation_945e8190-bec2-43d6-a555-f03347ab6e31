from flax import struct
import jax.numpy as jnp
from jax import lax

from .config.base import NetworkConfig

class InnovationTracker(struct.PyTreeNode):
    """
    Tracks the assignment and reuse of innovation numbers for new connections in NEAT.

    Attributes:
        next_innovation_id: Next available innovation number (scalar int32)
        connection_history: Matrix of [max_connections, 3] storing (sender, receiver, innovation_id) tuples.
                           Unused slots are filled with -1.
    """
    next_innovation_id: jnp.ndarray  # shape=(), dtype=int32
    connection_history: jnp.ndarray  # shape=(max_connections, 3), dtype=int32

    @classmethod
    def create(
        cls,
        config: NetworkConfig,
    ) -> 'InnovationTracker':
        """Initialize tracker with empty history and innovation counter.

        Args:
            config: NetworkConfig containing max_connections and other network parameters.

        Returns:
            A new InnovationTracker instance.
        """
        max_connections = config.max_connections
        
        return cls(
            next_innovation_id=jnp.array(0, dtype=jnp.int32),
            connection_history=jnp.full((max_connections, 3), -1, dtype=jnp.int32),
        )

    def get_innovation_id(
        self,
        sender: jnp.ndarray,
        receiver: jnp.ndarray,
    ) -> tuple[jnp.ndarray, 'InnovationTracker']:
        """Get or assign an innovation ID for a new connection.

        Args:
            sender: Sender node index (scalar or 1D array)
            receiver: Receiver node index (scalar or 1D array)

        Returns:
            Tuple of (innovation_id, updated_tracker)
            - innovation_id: Scalar innovation ID. Returns -1 if at capacity.
            - updated_tracker: New tracker state
        """
        # Convert inputs to scalars, handling both scalar and 1D array inputs
        sender_scalar = jnp.asarray(sender).flatten()[0]
        receiver_scalar = jnp.asarray(receiver).flatten()[0]
        
        # Check if we're at capacity
        num_connections = jnp.sum(self.connection_history[:, 0] != -1)
        at_capacity = num_connections >= self.connection_history.shape[0]
        
        # Check for existing connection
        sender_match = self.connection_history[:, 0] == sender_scalar
        receiver_match = self.connection_history[:, 1] == receiver_scalar
        match_mask = jnp.logical_and(sender_match, receiver_match)
        
        # Find first match
        match_idx = jnp.argmax(match_mask)
        match_found = jnp.any(match_mask)
        
        # Get existing innovation ID if found
        existing_id = self.connection_history[match_idx, 2]
        
        # Create new entry for new connections (all scalars now)
        new_entry = jnp.array([
            sender_scalar,
            receiver_scalar,
            self.next_innovation_id
        ], dtype=jnp.int32)
        
        # Update history only if no match found and not at capacity
        should_add_new = jnp.logical_and(~match_found, ~at_capacity)
        
        # Create mask for where to insert new entry
        insert_mask = jnp.logical_and(
            should_add_new,
            jnp.expand_dims(
                jnp.arange(self.connection_history.shape[0]) == num_connections,
                axis=1
            )
        )
        
        updated_history = jnp.where(
            insert_mask,
            new_entry,
            self.connection_history
        )
        
        # Determine innovation ID to return as scalar
        innovation_id = jnp.where(
            at_capacity,
            jnp.array(-1, dtype=jnp.int32),  # Return -1 if at capacity
            jnp.where(
                match_found,
                existing_id,
                self.next_innovation_id
            )
        )
        
        # Update tracker - only increment next_innovation_id for new connections
        updated_tracker = self.replace(
            next_innovation_id=jnp.where(
                jnp.logical_or(match_found, at_capacity),
                self.next_innovation_id,  # Don't increment if match found or at capacity
                self.next_innovation_id + 1  # Increment for new connections
            ),
            connection_history=updated_history
        )
        
        return innovation_id, updated_tracker