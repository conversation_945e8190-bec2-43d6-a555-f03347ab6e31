import jax
import jax.numpy as jnp

activation_fns_list = [
    lambda x: jax.nn.sigmoid(x),                        # 0: <PERSON>g<PERSON><PERSON> (NEAT default)
    lambda x: jnp.tanh(x),                              # 1: Tanh
    lambda x: 1.0 if x.ndim == 0 else jax.nn.softmax(x, axis=-1), # 2: Softmax (safe for scalars)
    lambda x: jax.nn.relu(x),                           # 3: ReLU
    lambda x: jax.nn.leaky_relu(x, negative_slope=0.01),# 4: Leaky ReLU
    lambda x: jnp.where(x > 0, x, jnp.exp(x) - 1),      # 5: ELU
    lambda x: x * jax.nn.sigmoid(x),                    # 6: Swish/SiLU
    lambda x: x,                                        # 7: Identity
]

def get_activation_fn(activation_index: int, x: jnp.ndarray) -> jnp.ndarray:
    """
    Given an index, selects an activation function and computes `activation(x)`.

    Available activation functions:
        0: jax.nn.sigmoid(x) (Sigmoid, NEAT default)
        1: jnp.tanh(x) (Tanh)
        2: jax.nn.softmax(x, axis=-1) (Softmax, only if x is not scalar)
        3: jax.nn.relu(x) (ReLU)
        4: jax.nn.leaky_relu(x, negative_slope=0.01) (Leaky ReLU)
        5: jnp.where(x > 0, x, jnp.exp(x) - 1) (ELU)
        6: x * jax.nn.sigmoid(x) (Swish/SiLU)
        7: x (Identity)
    """
    # Use jax.lax.switch instead of direct indexing for JAX compatibility
    return jax.lax.switch(
        activation_index,
        activation_fns_list,
        x
    )