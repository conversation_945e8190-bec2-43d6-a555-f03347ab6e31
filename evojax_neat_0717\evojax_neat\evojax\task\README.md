# Tasks

| Tasks                | Description                                                                                                                                                                                                                                                                                                                                                                                         | Contributors                                            | Example                                                                                                                                                                                                                                                                                                          |
|----------------------|-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| MNIST                | MNIST classification ([source](https://github.com/google/evojax/blob/main/evojax/task/mnist.py)).                                                                                                                                                                                                                                                                                                   | EvoJAX team                                             | [script](https://github.com/google/evojax/blob/main/examples/train_mnist.py)                                                                                                                                                                                                                                     |
| Seq2seq              | A simple addition problem in a [seq2seq](https://github.com/google/flax/tree/main/examples/seq2seq) setting ([source](https://github.com/google/evojax/blob/main/evojax/task/seq2seq.py)).  <br>E.g., the agent may see '012+345', and then upon receiving the prompt '=', it outputs '357'.                                                                                                        | EvoJAX team                                             | [script](https://github.com/google/evojax/blob/main/examples/train_seq2seq.py)                                                                                                                                                                                                                                   |
| CartPole Swing Up    | The classic control task where the goal is to swing up a pole mounted on a cart ([source](https://github.com/google/evojax/blob/main/evojax/task/cartpole.py)).<br>We provide easy and hard levels where the latter has its initial states sampled from a wider range.                                                                                                                              | EvoJAX team                                             | [script](https://github.com/google/evojax/blob/main/examples/train_cartpole.py)                                                                                                                                                                                                                                  |
| Robot Control (Brax) | Robot control training in the [Brax](https://github.com/google/brax) simulator ([source](https://github.com/google/evojax/blob/main/evojax/task/brax_task.py)).                                                                                                                                                                                                                                     | EvoJAX team                                             | [notebook](https://github.com/google/evojax/blob/main/examples/notebooks/BraxTasks.ipynb)                                                                                                                                                                                                                        |
| Water World          | In this [task](https://cs.stanford.edu/people/karpathy/reinforcejs/waterworld.html), the agent learns to catch as much food as possible while avoiding poisons ([source](https://github.com/google/evojax/blob/main/evojax/task/waterworld.py)).<br>We also show that it is possible for [multi-agent training](https://github.com/google/evojax/blob/main/evojax/task/ma_waterworld.py) in EvoJAX. | EvoJAX team                                             | [script1](https://github.com/google/evojax/blob/main/examples/train_waterworld.py)<br>[script2](https://github.com/google/evojax/blob/main/examples/train_waterworld_ma.py)                                                                                                                                      |
| Abstract Painting    | We reproduce a [computational creativity work](https://es-clip.github.io/) and highlight EvoJAX's modularity design in this task.                                                                                                                                                                                                                                                                   | EvoJAX team                                             | [notebook1](https://github.com/google/evojax/blob/main/examples/notebooks/AbstractPainting01.ipynb)<br>[notebook2](https://github.com/google/evojax/blob/main/examples/notebooks/AbstractPainting02.ipynb)<br>[notebook3](https://github.com/google/evojax/blob/main/examples/notebooks/HighResGIFfromSVG.ipynb) |
| Flocking             | We demonstrate a flocking system in EvoJAX where fishes learn to move in a coherent fashion ([source](https://github.com/google/evojax/blob/main/evojax/task/flocking.py)).<br>In this task, all fishes move at constant speed, they observe the states of the nearest K fishes and learn to orient.                                                                                                | [すずめ](https://github.com/mayu-snba19)<br>EvoJAX team | [notebook](https://github.com/google/evojax/blob/main/examples/notebooks/FlockingSimple.ipynb)                                                                                                                                                                                                                   |
| Slime Volleyball     | Slime Volleyball is a game created in the early 2000s by an unknown author. This task is designed for testing single and multi-agent learning algorithms. ([source](https://github.com/hardmaru/slimevolleygym)). | EvoJAX team | [script](https://github.com/google/evojax/blob/main/examples/train_slimevolley.py)                                                                                                                                                                                                                   |
