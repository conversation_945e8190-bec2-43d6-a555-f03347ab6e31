es_name: "ARS_native"
problem_type: "cartpole_easy"
normalize: false
es_config:
  pop_size: 100
  elite_ratio: 0.1
  init_stdev: 0.1
  decay_stdev: 0.999
  limit_stdev: 0.01
  optimizer: "sgd"
  optimizer_config:
    lrate_init: 0.05
    lrate_decay_steps: 1
    lrate_limit: 1e-3
    decay_coef: 1
hidden_size: 64
num_tests: 100
n_repeats: 16
max_iter: 1000
test_interval: 100
log_interval: 50
seed: 42
gpu_id: 0
debug: true