{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": []}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU", "gpuClass": "premium"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/google/evojax/blob/main/examples/notebooks/TutorialCallEvoJAXAlgorithmDirectly.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["# How to call EvoJAX algorithm directly\n", "\n", "In this notebook, we show how to call directly EvoJAX Algorithm, using CMA-ES as an example."], "metadata": {"id": "x642GwUg76S2"}}, {"cell_type": "code", "source": ["! nvidia-smi"], "metadata": {"id": "rXiUxxjTm1JQ", "outputId": "a7f424b8-ebb6-4f0f-c4f6-4349e37dbb53", "colab": {"base_uri": "https://localhost:8080/"}}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mon Feb 20 04:30:14 2023       \n", "+-----------------------------------------------------------------------------+\n", "| NVIDIA-SMI 510.47.03    Driver Version: 510.47.03    CUDA Version: 11.6     |\n", "|-------------------------------+----------------------+----------------------+\n", "| GPU  Name        Persistence-M| Bus-Id        Disp.A | Volatile Uncorr. ECC |\n", "| Fan  Temp  Perf  Pwr:Usage/Cap|         Memory-Usage | GPU-Util  Compute M. |\n", "|                               |                      |               MIG M. |\n", "|===============================+======================+======================|\n", "|   0  NVIDIA A100-SXM...  Off  | 00000000:00:04.0 Off |                    0 |\n", "| N/A   32C    P0    49W / 400W |      0MiB / 40960MiB |      0%      Default |\n", "|                               |                      |             Disabled |\n", "+-------------------------------+----------------------+----------------------+\n", "                                                                               \n", "+-----------------------------------------------------------------------------+\n", "| Processes:                                                                  |\n", "|  GPU   GI   CI        PID   Type   Process name                  GPU Memory |\n", "|        ID   ID                                                   Usage      |\n", "|=============================================================================|\n", "|  No running processes found                                                 |\n", "+-----------------------------------------------------------------------------+\n"]}]}, {"cell_type": "code", "execution_count": 2, "metadata": {"id": "3JUC1uiVzGS6"}, "outputs": [], "source": ["import sys\n", "IN_COLAB = 'google.colab' in sys.modules\n", "\n", "if IN_COLAB:\n", "  ! pip install --upgrade pip\n", "  ! pip install --upgrade flax==0.6.1\n", "  ! pip install --upgrade evojax\n", "else: \n", "  ! pip install --upgrade pip\n", "  ! pip install --upgrade evojax\n", "  ! pip install seaborn\n", "\n", "from IPython.display import clear_output\n", "clear_output()"]}, {"cell_type": "code", "source": ["import jax\n", "from jax import numpy as jnp\n", "\n", "import numpy as np\n", "\n", "from matplotlib import pyplot as plt\n", "import seaborn as sns"], "metadata": {"id": "vf7ArnQZzPKT"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["PARAM_SIZE = 2\n", "\n", "# define evaluation at a single point\n", "\n", "def f(x: jnp.ndarray) -> jnp.ndarray:\n", "  c = jnp.array([+0.3, -0.7])\n", "  \n", "  # # simple convex funciton\n", "  # y = - jnp.sum(jnp.abs(x - c))\n", "  # # make it harder:\n", "  d = jnp.sum(jnp.abs(x - c))\n", "  y = jnp.cos(d) * jnp.exp(-0.1*d)\n", "  # so y is maximized when x = c\n", "  return y\n", "\n", "# ... test it\n", "_x = jax.random.normal(\n", "    key=jax.random.<PERSON><PERSON><PERSON><PERSON>(123), \n", "    shape=(PARAM_SIZE,)\n", ")\n", "print(f'f({_x}) = {f(_x)}')\n", "\n", "# evalute f in batch\n", "f_batch = jax.vmap(f, in_axes=(0,), out_axes=0)\n", "\n", "# ... test it\n", "TEST_BATCH_SIZE = 3\n", "_x_batch = jax.random.normal(\n", "    key=jax.random.<PERSON><PERSON><PERSON><PERSON>(123), \n", "    shape=(TEST_BATCH_SIZE, PARAM_SIZE,)\n", ")\n", "print(f'f_batch(\\n{_x_batch}\\n) = \\n{f_batch(_x_batch)}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "OwB3qq2kzv66", "outputId": "f8c34d65-1aed-484c-e763-a86d79705477"}, "execution_count": 57, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["f([-0.03049826  0.49289012]) = 0.040693823248147964\n", "f_batch(\n", "[[-0.29256076 -0.2805558 ]\n", " [-0.78786534 -0.90408975]\n", " [ 0.89289093  0.75706756]]\n", ") = \n", "[ 0.4791347   0.2418831  -0.37558314]\n"]}]}, {"cell_type": "code", "source": ["from evojax.algo import PGPE, CMA_ES_JAX"], "metadata": {"id": "9ZITYoam0m5A"}, "execution_count": 58, "outputs": []}, {"cell_type": "code", "source": ["n_population = 128\n", "n_feature = PARAM_SIZE\n", "n_iterations = 40\n", "report_interval = 1"], "metadata": {"id": "X-4GFXmK8PJd"}, "execution_count": 59, "outputs": []}, {"cell_type": "code", "source": ["#@title CMA ES without particular initilization\n", "\n", "solver = CMA_ES_JAX(\n", "  pop_size=n_population,\n", "  param_size=n_feature,\n", "  init_stdev=0.1,\n", ")\n", "\n", "have_printed = False\n", "recorded_best_fitness_no_init = []\n", "\n", "for iter_ in range(1, 1 + n_iterations):\n", "  solutions = solver.ask() \n", "  fitnesses = f_batch(solutions)\n", "  solver.tell(fitnesses)\n", "\n", "  if iter_ % report_interval == 0 or iter_ == 1 or iter_ == n_iterations:\n", "    best_solutions = jnp.expand_dims(solver.best_params, axis=0)\n", "    best_fitnesses = f_batch(best_solutions)\n", "    recorded_best_fitness_no_init.append(\n", "        np.array(best_fitnesses)[0]\n", "    )\n", "\n", "    if not have_printed:\n", "      have_printed = True\n", "      print(f'solutions.shape = {solutions.shape}')\n", "      print(f'fitnesses.shape = {fitnesses.shape}')\n", "      print(f'best_solutions.shape = {best_solutions.shape}')\n", "\n", "    print(f'f_batch({best_solutions}) = {best_fitnesses}')"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "wKFjDqCb2mz9", "outputId": "c672c2d3-02e3-4abe-dc1d-e47ece633ccc"}, "execution_count": 60, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["solutions.shape = (128, 2)\n", "fitnesses.shape = (128,)\n", "best_solutions.shape = (1, 2)\n", "f_batch([[ 0.08474442 -0.073248  ]]) = [0.6121875]\n", "f_batch([[ 0.17500642 -0.15183967]]) = [0.7309605]\n", "f_batch([[ 0.3509241  -0.40125412]]) = [0.9072026]\n", "f_batch([[ 0.39421523 -0.58534455]]) = [0.95804447]\n", "f_batch([[ 0.32162884 -0.6903529 ]]) = [0.9963898]\n", "f_batch([[ 0.3269463 -0.7115207]]) = [0.9954238]\n", "f_batch([[ 0.3014449 -0.7109104]]) = [0.998689]\n", "f_batch([[ 0.29477358 -0.69933814]]) = [0.99939406]\n", "f_batch([[ 0.2975316 -0.6987489]]) = [0.9996212]\n", "f_batch([[ 0.29991594 -0.6995431 ]]) = [0.9999458]\n", "f_batch([[ 0.29953265 -0.7003363 ]]) = [0.99991935]\n", "f_batch([[ 0.2997769 -0.7002779]]) = [0.99994975]\n", "f_batch([[ 0.2999973 -0.6999719]]) = [0.9999969]\n", "f_batch([[ 0.30000764 -0.6999761 ]]) = [0.9999969]\n", "f_batch([[ 0.30001923 -0.69999397]]) = [0.9999975]\n", "f_batch([[ 0.29999873 -0.70000386]]) = [0.99999946]\n", "f_batch([[ 0.30000415 -0.70000184]]) = [0.99999934]\n", "f_batch([[ 0.30000073 -0.70000076]]) = [0.9999999]\n", "f_batch([[ 0.30000108 -0.6999995 ]]) = [0.9999999]\n", "f_batch([[ 0.29999998 -0.7000002 ]]) = [1.]\n", "f_batch([[ 0.2999999 -0.6999999]]) = [1.]\n", "f_batch([[ 0.29999992 -0.6999999 ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.7       ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.7000001 ]]) = [1.]\n", "f_batch([[ 0.29999992 -0.7000001 ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.7000001 ]]) = [1.]\n", "f_batch([[ 0.30000004 -0.70000005]]) = [1.]\n", "f_batch([[ 0.30000007 -0.7       ]]) = [1.]\n", "f_batch([[ 0.3000001 -0.7      ]]) = [1.]\n", "f_batch([[ 0.30000007 -0.7       ]]) = [1.]\n", "f_batch([[ 0.30000004 -0.7       ]]) = [1.]\n", "f_batch([[ 0.3 -0.7]]) = [1.]\n", "f_batch([[ 0.3 -0.7]]) = [1.]\n", "f_batch([[ 0.29999998 -0.7       ]]) = [1.]\n", "f_batch([[ 0.29999992 -0.7       ]]) = [1.]\n", "f_batch([[ 0.2999999  -0.70000005]]) = [1.]\n", "f_batch([[ 0.2999999 -0.7000001]]) = [1.]\n", "f_batch([[ 0.29999995 -0.70000005]]) = [1.]\n", "f_batch([[ 0.29999992 -0.7000001 ]]) = [1.]\n", "f_batch([[ 0.29999992 -0.70000005]]) = [1.]\n"]}]}, {"cell_type": "code", "source": ["#@title CMA ES with initilization\n", "n_population = 128\n", "n_feature = PARAM_SIZE\n", "n_iterations = 40\n", "report_interval = 1\n", "\n", "solver = CMA_ES_JAX(\n", "  pop_size=n_population,\n", "  param_size=n_feature,\n", "  mean=jnp.array([0.35, -0.65]),\n", "  init_stdev=0.1,\n", ")\n", "\n", "have_printed = False\n", "recorded_best_fitness_with_init = []\n", "\n", "for iter_ in range(1, 1 + n_iterations):\n", "  solutions = solver.ask() \n", "  fitnesses = f_batch(solutions)\n", "  solver.tell(fitnesses)\n", "\n", "  if iter_ % report_interval == 0 or iter_ == 1 or iter_ == n_iterations:\n", "    best_solutions = jnp.expand_dims(solver.best_params, axis=0)\n", "    best_fitnesses = f_batch(best_solutions)\n", "    recorded_best_fitness_with_init.append(\n", "        np.array(best_fitnesses)[0]\n", "    )\n", "\n", "    if not have_printed:\n", "      have_printed = True\n", "      print(f'solutions.shape = {solutions.shape}')\n", "      print(f'fitnesses.shape = {fitnesses.shape}')\n", "      print(f'best_solutions.shape = {best_solutions.shape}')\n", "\n", "    print(f'f_batch({best_solutions}) = {best_fitnesses}')\n"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "5mckL7OD2uLK", "outputId": "6201242f-408e-4726-aea1-f865dd0e0ca5"}, "execution_count": 61, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["solutions.shape = (128, 2)\n", "fitnesses.shape = (128,)\n", "best_solutions.shape = (1, 2)\n", "f_batch([[ 0.30595613 -0.6888452 ]]) = [0.99814427]\n", "f_batch([[ 0.29828358 -0.6956377 ]]) = [0.99937385]\n", "f_batch([[ 0.2994544 -0.7001903]]) = [0.9999261]\n", "f_batch([[ 0.29997295 -0.70111954]]) = [0.9998847]\n", "f_batch([[ 0.29916427 -0.7002495 ]]) = [0.99989086]\n", "f_batch([[ 0.3000644 -0.7009134]]) = [0.9999017]\n", "f_batch([[ 0.3001192  -0.70024765]]) = [0.9999633]\n", "f_batch([[ 0.29999012 -0.7001805 ]]) = [0.9999809]\n", "f_batch([[ 0.3000445  -0.70005876]]) = [0.9999897]\n", "f_batch([[ 0.30002174 -0.7000235 ]]) = [0.99999547]\n", "f_batch([[ 0.29999495 -0.7000103 ]]) = [0.99999845]\n", "f_batch([[ 0.30000436 -0.70000714]]) = [0.99999887]\n", "f_batch([[ 0.3000021  -0.69999635]]) = [0.99999946]\n", "f_batch([[ 0.30000085 -0.6999991 ]]) = [0.99999976]\n", "f_batch([[ 0.29999956 -0.6999996 ]]) = [0.9999999]\n", "f_batch([[ 0.3       -0.6999999]]) = [1.]\n", "f_batch([[ 0.29999992 -0.6999999 ]]) = [1.]\n", "f_batch([[ 0.30000007 -0.6999999 ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.69999987]]) = [1.]\n", "f_batch([[ 0.3        -0.69999987]]) = [1.]\n", "f_batch([[ 0.30000004 -0.7       ]]) = [1.]\n", "f_batch([[ 0.3000001 -0.7      ]]) = [1.]\n", "f_batch([[ 0.30000016 -0.7       ]]) = [1.]\n", "f_batch([[ 0.3000001 -0.7      ]]) = [1.]\n", "f_batch([[ 0.30000004 -0.70000005]]) = [1.]\n", "f_batch([[ 0.3        -0.70000005]]) = [1.]\n", "f_batch([[ 0.30000004 -0.7       ]]) = [1.]\n", "f_batch([[ 0.30000007 -0.7       ]]) = [1.]\n", "f_batch([[ 0.30000004 -0.7       ]]) = [1.]\n", "f_batch([[ 0.3 -0.7]]) = [1.]\n", "f_batch([[ 0.29999998 -0.7       ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.7       ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.7       ]]) = [1.]\n", "f_batch([[ 0.29999992 -0.7       ]]) = [1.]\n", "f_batch([[ 0.2999999  -0.70000005]]) = [1.]\n", "f_batch([[ 0.29999992 -0.7000001 ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.70000017]]) = [1.]\n", "f_batch([[ 0.29999995 -0.7000001 ]]) = [1.]\n", "f_batch([[ 0.29999995 -0.70000017]]) = [1.]\n", "f_batch([[ 0.29999992 -0.7000001 ]]) = [1.]\n"]}]}, {"cell_type": "code", "source": ["plt.plot(recorded_best_fitness_no_init, label='w/o init')\n", "plt.plot(recorded_best_fitness_with_init, label='w/ init')\n", "plt.legend()\n", "plt.title('Curve for best fitness')\n", "plt.show()"], "metadata": {"id": "Lo3jBjzS6xNZ", "colab": {"base_uri": "https://localhost:8080/", "height": 281}, "outputId": "1864552e-d1df-4d2d-b6ff-fc279416c5ae"}, "execution_count": 62, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["<Figure size 432x288 with 1 Axes>"], "image/png": "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\n"}, "metadata": {"needs_background": "light"}}]}, {"cell_type": "code", "source": [], "metadata": {"id": "MGgaMJAw8iYc"}, "execution_count": 56, "outputs": []}]}