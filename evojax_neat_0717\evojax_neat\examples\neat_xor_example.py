"""
NEAT XOR Example - Testing NEAT on the classic XOR problem.
Inherited from working hybrid-neat-0707 implementation.
"""

import os
import jax
import jax.numpy as jnp
import time
import logging
from evojax.algo import NEAT
from evojax.algo.neat_core.config import XOR_CONFIG
from evojax.algo.neat_core.network import extract_single_network, ActivationState
from evojax.algo.neat_core.loss_fns import mean_squared_error

# Setup logging (inherited from working version)
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)

# XOR dataset (exact same as working version)
XOR_INPUTS = jnp.array([[-1.0, -1.0], [-1.0, 1.0], [1.0, -1.0], [1.0, 1.0]])
XOR_TARGETS = jnp.array([[-1.0], [1.0], [1.0], [-1.0]])

def evaluate_xor_fitness(*args) -> float:
    """
    XOR fitness function - INHERITED from working hybrid-neat-0707
    Handles both backprop and NEAT calling conventions
    """
    try:
        if len(args) == 2:
            predictions, targets = args
            mse = mean_squared_error(predictions, targets)
            return 4.0 - mse
        elif len(args) == 3:
            network, activation_state, _ = args
            outputs, _ = network.forward(XOR_INPUTS, activation_state)
            mse = mean_squared_error(outputs, XOR_TARGETS)
            return 4.0 - mse
        else:
            raise ValueError(f"Invalid number of arguments: {len(args)}. Expected 2 or 3.")
    except Exception as e:
        logger.error(f"Error in fitness evaluation: {e}")
        return -jnp.inf

def calculate_xor_accuracy(network, activation_state) -> float:
    """Calculate XOR accuracy - INHERITED from working version"""
    try:
        correct = 0
        for i, input_pair in enumerate(XOR_INPUTS):
            output, _ = network.forward(input_pair.reshape(1, -1), activation_state)
            expected = XOR_TARGETS[i][0]
            actual = output[0, 0]
            if (expected * actual) > 0:  # Same sign means correct
                correct += 1
        return (correct / len(XOR_INPUTS)) * 100.0
    except Exception as e:
        logger.error(f"Error calculating accuracy: {e}")
        return 0.0

def evaluate_batch_fitness(networks):
    """Evaluate fitness for batch of networks"""
    batch_size = networks.connections.shape[0]
    fitness_scores = []

    for i in range(batch_size):
        single_network = extract_single_network(networks, i)
        activation_state = ActivationState(
            node_depths=jnp.full(single_network.max_nodes, -1, dtype=jnp.int32),
            outdated_depths=True
        )
        # Call with 3 arguments for NEAT mode: (network, activation_state, inputs)
        fitness = evaluate_xor_fitness(single_network, activation_state, XOR_INPUTS)
        fitness_scores.append(fitness)

    return jnp.array(fitness_scores)

def main():
    # Create log directory for XOR results
    log_dir = './log/xor'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # Only log once
    logger.info("🚀 Starting XOR Evolution")
    logger.info(f"Population: {XOR_CONFIG.population.population_size}")
    logger.info(f"Max Generations: {XOR_CONFIG.max_generations}")
    logger.info(f"Backprop: {XOR_CONFIG.backprop.rounds} rounds @ LR {XOR_CONFIG.backprop.learning_rate}")
    logger.info(f"JAX Backend: {jax.default_backend()}")
    logger.info("-" * 60)
    
    # Use the working pattern from the task files with visualization enabled
    from evojax.algo.neat_core.tasks.xor_task import run_xor_evolution
    
    try:
        # Run evolution using the working implementation WITH VISUALIZATION
        results = run_xor_evolution(
            config=XOR_CONFIG, 
            seed=42, 
            enable_viz=True
        )
        
        # Display results in the same style as the original hybrid-neat-0707
        print("\n" + "=" * 80)
        print("🎯 XOR EVOLUTION RESULTS")
        print("=" * 80)
        
        if results.success:
            print("🎉 SUCCESS - PERFECT SOLUTION")
            
            print(f"📊 Performance Metrics:")
            print(f"   Generation:         {results.generation}")
            print(f"   Fitness Score:      {results.fitness:.6f} / 4.000000")
            print(f"   Classification:     {results.accuracy:.1f}% accuracy")
            print(f"   Total Runtime:      {results.total_time:.2f} seconds")
            print(f"   Configuration:      {results.configuration_name}")
            print()
            
            # XOR test cases
            print(f"🔍 XOR Truth Table Verification:")
            print(f"   {'Input':<12} {'Expected':<10} {'Actual':<12} {'Error':<10} {'Status'}")
            print(f"   {'-'*12} {'-'*10} {'-'*12} {'-'*10} {'-'*6}")
            
            for case_name, case_data in results.xor_outputs.items():
                status = "✓ PASS" if case_data['correct'] else "✗ FAIL"
                status_color = "\033[92m" if case_data['correct'] else "\033[91m"
                reset_color = "\033[0m"
                
                print(f"   {case_name:<12} {case_data['expected']:>+8.3f}  "
                      f"{case_data['actual']:>+10.6f}  {case_data['error']:>8.6f}  "
                      f"{status_color}{status}{reset_color}")
            
            print()
            
            # Performance analysis
            print(f"⚡ Performance Analysis:")
            efficiency_rating = "EXCELLENT" if results.generation <= 5 else "GOOD" if results.generation <= 10 else "MODERATE"
            print(f"   Convergence Speed:  {efficiency_rating} ({results.generation} generations)")
            print(f"   Solution Quality:   {'PERFECT' if results.fitness >= 3.99 else 'HIGH' if results.fitness >= 3.8 else 'MODERATE'}")
            print(f"   Runtime Efficiency: {results.total_time/results.generation:.2f}s per generation")
            
            if results.fitness >= 3.99:
                print(f"\n🚀 This configuration demonstrates the power of hybrid neuroevolution!")
                print(f"   The combination of aggressive mutation + backpropagation achieves")
                print(f"   perfect XOR performance in just {results.generation} generations.")
            
        else:
            print("⚠️ Evolution did not achieve target fitness")
            print(f"   Best fitness achieved: {results.fitness:.6f}")
            print(f"   Target fitness: {XOR_CONFIG.target_fitness}")
            
    except ImportError as e:
        # Fallback to manual implementation if task module not available
        logger.warning(f"Task module not found: {e}, using manual implementation")
        run_manual_evolution(log_dir)
    except Exception as e:
        logger.error(f"Error in run_xor_evolution: {e}")
        logger.warning("Falling back to manual implementation")
        run_manual_evolution(log_dir)

def run_manual_evolution(log_dir='./log/xor'):
    """Manual evolution implementation - fallback when task module not available"""
    # Use the direct evolution approach like the working version
    from evojax.algo.neat_core import (
        initialize_population, InnovationTracker, convert_genomes_to_networks,
        initialize_state, evolve_population
    )

    key = jax.random.PRNGKey(42)
    tracker = InnovationTracker.create(XOR_CONFIG.network)

    key, init_key = jax.random.split(key)
    population, tracker = initialize_population(
        key=init_key,
        innovation_tracker=tracker,
        config=XOR_CONFIG
    )

    networks, _ = convert_genomes_to_networks(
        connections=population.connections,
        enabled=population.enabled,
        config=XOR_CONFIG
    )

    key, species_key = jax.random.split(key)
    species_state = initialize_state(
        connections=population.connections,
        key=species_key,
        config=XOR_CONFIG.species
    )

    start_time = time.time()
    best_fitness = -jnp.inf
    best_network = None
    best_generation = 0

    logger.info("Evolution started...")
    logger.info(f"📊 Visualizations will be saved to: results/xor")

    for generation in range(XOR_CONFIG.max_generations):
        gen_start_time = time.time()

        # Evolve population using the same approach as the working version
        networks, species_state, tracker, fitnesses, key = evolve_population(
            networks=networks,
            species_state=species_state,
            tracker=tracker,
            key=key,
            fitness_fn=evaluate_xor_fitness,
            inputs=XOR_INPUTS,
            config=XOR_CONFIG
        )

        # Track best for logging
        current_best_idx = jnp.argmax(fitnesses)
        current_best_fitness = float(fitnesses[current_best_idx])

        if current_best_fitness > best_fitness:
            best_fitness = current_best_fitness
            best_network = extract_single_network(networks, current_best_idx)
            best_generation = generation

        # Log progress
        gen_elapsed_time = time.time() - gen_start_time
        total_elapsed = time.time() - start_time

        # Get species info
        num_species = jnp.sum(species_state.active_mask)
        diversity = jnp.std(fitnesses)

        # Determine if this is a breakthrough (fitness >= 3.0)
        is_breakthrough = current_best_fitness >= 3.0
        breakthrough_text = " - 🔥 BREAKTHROUGH" if is_breakthrough else ""

        logger.info(
            f"Gen {generation:2d}: Current={current_best_fitness:.4f}, "
            f"Best={best_fitness:.4f} (Gen {best_generation}), "
            f"Species={num_species}, Diversity={diversity:.3f}, "
            f"Time={gen_elapsed_time:.1f}s{breakthrough_text}"
        )
        
        # Early termination for perfect solution
        if best_fitness >= 3.99:
            logger.info(f"🎉 PERFECT SOLUTION ACHIEVED! Terminating early at generation {generation}")
            break
    
    # Final results
    total_time = time.time() - start_time
    if best_network:
        activation_state = ActivationState(
            node_depths=jnp.full(best_network.max_nodes, -1, dtype=jnp.int32),
            outdated_depths=True
        )
        accuracy = calculate_xor_accuracy(best_network, activation_state)
        
        print("\n" + "=" * 80)
        print("🎯 XOR EVOLUTION RESULTS")
        print("=" * 80)
        
        if best_fitness >= 3.99:
            print("🎉 SUCCESS - PERFECT SOLUTION")
            
            print(f"📊 Performance Metrics:")
            print(f"   Generation:         {best_generation}")
            print(f"   Fitness Score:      {best_fitness:.6f} / 4.000000")
            print(f"   Classification:     {accuracy:.1f}% accuracy")
            print(f"   Total Runtime:      {total_time:.2f} seconds")
            print(f"   Configuration:      XOR Standard")
            print()
            
            # XOR test cases
            print(f"🔍 XOR Truth Table Verification:")
            print(f"   {'Input':<12} {'Expected':<10} {'Actual':<12} {'Error':<10} {'Status'}")
            print(f"   {'-'*12} {'-'*10} {'-'*12} {'-'*10} {'-'*6}")
            
            xor_outputs = {}
            for i, input_pair in enumerate(XOR_INPUTS):
                output, _ = best_network.forward(input_pair.reshape(1, -1), activation_state)
                expected = XOR_TARGETS[i][0]
                actual = float(output[0, 0])
                error = abs(expected - actual)
                correct = (expected * actual) > 0
                
                case_name = f"XOR({input_pair[0]:+.0f},{input_pair[1]:+.0f})"
                xor_outputs[case_name] = {
                    'expected': expected,
                    'actual': actual,
                    'error': error,
                    'correct': correct
                }
                
                status = "✓ PASS" if correct else "✗ FAIL"
                status_color = "\033[92m" if correct else "\033[91m"
                reset_color = "\033[0m"
                
                print(f"   {case_name:<12} {expected:>+8.3f}  "
                      f"{actual:>+10.6f}  {error:>8.6f}  "
                      f"{status_color}{status}{reset_color}")
            
            print()
            
            # Performance analysis
            print(f"⚡ Performance Analysis:")
            efficiency_rating = "EXCELLENT" if best_generation <= 5 else "GOOD" if best_generation <= 10 else "MODERATE"
            print(f"   Convergence Speed:  {efficiency_rating} ({best_generation} generations)")
            print(f"   Solution Quality:   {'PERFECT' if best_fitness >= 3.99 else 'HIGH' if best_fitness >= 3.8 else 'MODERATE'}")
            print(f"   Runtime Efficiency: {total_time/best_generation:.2f}s per generation")
            
            if best_fitness >= 3.99:
                print(f"\n🚀 This configuration demonstrates the power of hybrid neuroevolution!")
                print(f"   The combination of aggressive mutation + backpropagation achieves")
                print(f"   perfect XOR performance in just {best_generation} generations.")
        else:
            print("⚠️ Evolution did not achieve target fitness")
            print(f"   Best fitness achieved: {best_fitness:.6f}")
            print(f"   Target fitness: {XOR_CONFIG.target_fitness}")
            
            # XOR test cases
            print(f"🔍 XOR Truth Table Verification:")
            for i, input_pair in enumerate(XOR_INPUTS):
                output, _ = best_network.forward(input_pair.reshape(1, -1), activation_state)
                expected = XOR_TARGETS[i][0]
                actual = float(output[0, 0])
                error = abs(expected - actual)
                correct = (expected * actual) > 0
                status = "✓ PASS" if correct else "✗ FAIL"
                status_color = "\033[92m" if correct else "\033[91m"
                reset_color = "\033[0m"
                print(f"   XOR({input_pair[0]:+.0f},{input_pair[1]:+.0f}): {expected:+.3f} → {actual:+.6f} {status_color}{status}{reset_color}")

if __name__ == "__main__":
    main()
