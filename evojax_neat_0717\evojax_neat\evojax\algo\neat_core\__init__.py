"""
NEAT (NeuroEvolution of Augmenting Topologies) core implementation for EvoJAX.

This module provides the core NEAT algorithm components adapted for use with EvoJAX.
The implementation is based on the hybrid NEAT version with JAX optimizations.

Core Components:
- Network: Represents a neural network with its connections and nodes.
- Population: Manages a collection of networks (genomes).
- Evolution: Contains functions to run the evolutionary process.
- Speciation: Handles the division of the population into species.
- Mutations: Provides functions for mutating network structures and weights.
- Crossover: Implements genetic recombination between parent networks.
- Innovation: Tracks new structural innovations (genes).
- Configuration: Dataclasses for configuring various aspects of NEAT.
- Backpropagation: Hybrid NEAT with backpropagation training capabilities.
- Activation Functions: Various neural network activation functions.
- Loss Functions: Loss functions for training and evaluation.
"""

__version__ = "0.1.0"

# --- Configuration --- #
from .config.base import (
    NetworkConfig,
    PopulationConfig,
    SpeciesConfig,
    RecombinationConfig,
    MutationConfig,
    FitnessConfig,
    BackpropConfig,
    NEATConfig
)
from .config.tasks.xor import XOR_CONFIG
from .default_config import DEFAULT_NEAT_CONFIG, create_neat_config

# --- Innovation Tracking --- #
from .innovation import InnovationTracker

# === MAIN OPERATIONS (in logical order) === #

# --- Core Population Management --- #
from .population import (
    Population,
    initialize_population,
    convert_genomes_to_networks
)

# --- Core Network --- #
from .network import (
    Network,
    NetworkBatch,
    ActivationState,
    update_depth,
    init_network
)

# --- Speciation --- #
from .species import (
    SpeciesState,
    initialize_state as initialize_species_state,
    compute_compatibility_distance,
    compute_pairwise_distances,
    assign_species,
    allocate_offspring,
    update_species
)

# --- Parent Selection and Crossover --- #
from .parent_selection_and_crossover import (
    select_parents,
    crossover,
    recombine
)

# --- Mutations --- #
from .mutations import (
    shift_weights,
    add_node,
    add_connection,
    mutate,
    mutate_networks
)

# --- Backpropagation and Fitness Evaluation --- #
from .backpropagation_and_fitness_evaluation import (
    evaluate_fitness,
    create_optimizer,
    train_network,
    optimize_weights_with_backprop,
    evaluate_networks
)

# --- Evolution --- #
from .evolution import (
    evolve_population,
    run_evolution
)

# --- Visualization --- #
from .visualization import (
    plot_fitness_history,
    visualize_network,
    create_visualization_hook
)

# === AUXILIARY COMPONENTS === #

# --- Activation Functions --- #
from .activation_fns import (
    get_activation_fn,
    activation_fns_list
)

# --- Loss Functions --- #
from .loss_fns import (
    binary_cross_entropy,
    mean_squared_error,
    softmax_cross_entropy,
    LossFn
)

# --- Utilities --- #
from .utils import (
    find_first,
    check_probability,
    sample_from_mask
)

# --- Constants --- #
from .constants import (
    NODE_BIAS,
    NODE_INPUT,
    NODE_OUTPUT,
    NODE_HIDDEN,
    NODE_UNUSED,
    ACT_FN_MAP,
    ACTIVATION_RELU,
    ACTIVATION_SIGMOID,
    ACTIVATION_IDENTITY
)

# Define what is available for `from neat_core import *`
__all__ = [
    # Configuration
    "NetworkConfig",
    "PopulationConfig", 
    "SpeciesConfig",
    "RecombinationConfig",
    "MutationConfig",
    "FitnessConfig",
    "BackpropConfig",
    "NEATConfig",
    "XOR_CONFIG",
    "DEFAULT_NEAT_CONFIG",
    "create_neat_config",

    # Innovation
    "InnovationTracker",

    # === MAIN OPERATIONS === #
    
    # Core Population & Network
    "Population",
    "initialize_population",
    "convert_genomes_to_networks",
    "Network",
    "NetworkBatch",
    "ActivationState",
    "update_depth",
    "init_network",

    # Speciation
    "SpeciesState",
    "initialize_species_state",
    "compute_compatibility_distance",
    "compute_pairwise_distances",
    "assign_species",
    "allocate_offspring",
    "update_species",

    # Parent Selection & Crossover
    "select_parents",
    "crossover",
    "recombine",

    # Mutations
    "shift_weights",
    "add_node",
    "add_connection",
    "mutate",
    "mutate_networks",

    # Backpropagation and Fitness Evaluation
    "evaluate_fitness",
    "create_optimizer",
    "train_network",
    "optimize_weights_with_backprop",
    "evaluate_networks",

    # Evolution
    "evolve_population",
    "run_evolution",

    # Visualization
    "plot_fitness_history",
    "visualize_network",
    "create_visualization_hook",

    # === AUXILIARY COMPONENTS === #

    # Activation Functions
    "get_activation_fn",
    "activation_fns_list",

    # Loss Functions
    "binary_cross_entropy",
    "mean_squared_error",
    "softmax_cross_entropy",
    "LossFn",

    # Utilities
    "find_first",
    "check_probability",
    "sample_from_mask",

    # Constants
    "NODE_BIAS",
    "NODE_INPUT",
    "NODE_OUTPUT",
    "NODE_HIDDEN",
    "NODE_UNUSED",
    "ACT_FN_MAP",
    "ACTIVATION_RELU",
    "ACTIVATION_SIGMOID",
    "ACTIVATION_IDENTITY",
]
