"""
Example demonstrating the NEAT algorithm interface with EvoJAX.

This example shows how to use the NEAT algorithm through the EvoJAX interface
for a simple optimization task.
"""

import jax
import jax.numpy as jnp
import numpy as np
from evojax.algo import NEAT
from evojax.algo.neat_core import XOR_<PERSON>NFIG


def simple_fitness_function(params):
    """A simple fitness function for demonstration.
    
    This function treats the parameter vector as weights and computes
    a simple quadratic fitness to demonstrate the ask/tell interface.
    """
    # Simple quadratic function with optimum at zero
    return -jnp.sum(params**2)


def main():
    """Main function demonstrating NEAT algorithm usage."""
    print("NEAT Algorithm Example with EvoJAX Interface")
    print("=" * 50)
    
    # Create NEAT configuration with backpropagation disabled for this simple example
    from evojax.algo.neat_core.config.base import NEATConfig, NetworkConfig, BackpropConfig, PopulationConfig
    
    config = NEATConfig(
        network=NetworkConfig(
            num_inputs=2,
            num_outputs=1,
            max_nodes=50,
            max_connections=50
        ),
        backprop=BackpropConfig(enabled=False),  # Disable backprop for simple fitness
        population=PopulationConfig(population_size=100)  # Fixed: use population config
    )
    
    # Initialize NEAT algorithm
    neat = NEAT(config=config, seed=42)
    
    print(f"Population size: {neat.pop_size}")
    print(f"Parameter size: {neat.param_size}")
    print(f"Network architecture: {neat.config.network.num_inputs} inputs, {neat.config.network.num_outputs} outputs")
    print()
    
    # Run a few generations
    num_generations = 5
    
    for generation in range(num_generations):
        print(f"Generation {generation + 1}")
        print("-" * 20)
        
        # Ask for population parameters
        params = neat.ask()
        print(f"Parameter shape: {params.shape}")
        
        # Evaluate fitness for each individual
        # In a real application, this would be your task-specific evaluation
        fitness_values = jnp.array([simple_fitness_function(p) for p in params])
        
        # Report fitness back to the algorithm
        neat.tell(fitness_values)
        
        # Get population information
        info = neat.get_population_info()
        print(f"Number of species: {info['num_species']}")
        print(f"Innovation count: {info['innovation_count']}")
        
        # Show fitness statistics
        best_fitness = jnp.max(fitness_values)
        mean_fitness = jnp.mean(fitness_values)
        print(f"Best fitness: {best_fitness:.4f}")
        print(f"Mean fitness: {mean_fitness:.4f}")
        print()
    
    # Get the best parameters found
    best_params = neat.best_params
    print(f"Final best parameters shape: {best_params.shape}")
    print(f"Final best fitness: {simple_fitness_function(best_params):.4f}")
    
    # Demonstrate accessing the actual NEAT networks
    networks = neat.get_networks()
    print(f"Network batch shape: {networks.connections.shape}")
    print(f"Network enabled connections: {networks.enabled.shape}")
    
    print("\nExample completed successfully!")


if __name__ == "__main__":
    main()



