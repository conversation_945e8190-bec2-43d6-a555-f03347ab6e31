import jax
import jax.numpy as jnp
from typing import Callable

def binary_cross_entropy(predictions: jnp.ndarray, targets: jnp.ndar<PERSON>) -> float:
    """For binary classification. Assumes predictions are already probabilities (0-1 range)."""
    # Predictions should already be probabilities from sigmoid activation
    # Clamp probabilities to avoid log(0)
    probs = jnp.clip(predictions, 1e-7, 1 - 1e-7)
    # Convert targets from {-1, 1} to {0, 1} if needed
    targets_01 = jnp.where(targets == -1, 0, targets)
    targets_01 = jnp.where(targets_01 == 1, 1, targets_01)
    # Calculate binary cross entropy
    bce = -(targets_01 * jnp.log(probs) + (1 - targets_01) * jnp.log(1 - probs))
    return jnp.mean(bce)

def mean_squared_error(predictions: jnp.ndarray, targets: jnp.ndarray) -> float:
    """For regression tasks."""
    return jnp.mean(jnp.square(predictions - targets))

def softmax_cross_entropy(logits: jnp.ndarray, targets: jnp.ndarray) -> float:
    """For multi-class classification with integer labels."""
    return jnp.mean(jax.nn.softmax_cross_entropy_with_integer_labels(logits, targets))

LossFn = Callable[[jnp.ndarray, jnp.ndarray], float]