import jax
import jax.numpy as jnp
import chex
from flax import struct
import logging

from functools import partial
from .config.base import SpeciesConfig

logger = logging.getLogger(__name__)

@struct.dataclass
class SpeciesState:
    """State for managing species in the population."""
    species_ids: jnp.ndarray        # Species ID for each member
    next_species_id: int            # Next available species ID
    active_mask: jnp.ndarray        # Which species are still active
    member_masks: jnp.ndarray       # Binary masks for species membership
    representatives: jnp.ndarray    # Representative genomes for each species
    offspring_counts: jnp.ndarray   # Number of offspring allocated to each species
    best_fitnesses: jnp.ndarray     # Best fitness achieved by each species
    last_improvements: jnp.ndarray  # Generations since last improvement (-1=inactive, 0=current improvement, ≥1=stagnation count)
    rng_key: chex.PRNGKey           # Random number generator key

@partial(jax.jit, static_argnames=['config'])
def initialize_state(
    connections: jnp.ndarray,
    key: chex.PRNGKey,
    config: SpeciesConfig
) -> SpeciesState:
    # Extract config parameters
    max_species = config.max_species
    
    pop_size = connections.shape[0]
    return SpeciesState(
        species_ids=jnp.full(pop_size, -1, dtype=jnp.int32),
        next_species_id=0,
        active_mask=jnp.zeros(max_species, dtype=jnp.bool_),
        member_masks=jnp.zeros((max_species, pop_size), dtype=jnp.bool_),
        representatives=jnp.zeros((max_species, *connections.shape[1:])),
        offspring_counts=jnp.zeros(max_species, dtype=jnp.int32),
        best_fitnesses=jnp.full(max_species, -jnp.inf, dtype=jnp.float32),
        last_improvements=jnp.full(max_species, -1, dtype=jnp.int32),
        rng_key=key
    )

@partial(jax.jit, static_argnames=['config'])
def compute_compatibility_distance(
    connections1: jnp.ndarray,
    enabled1: jnp.ndarray,
    connections2: jnp.ndarray,
    enabled2: jnp.ndarray,
    config: SpeciesConfig
) -> float:
    """Compute compatibility distance between two genomes using connection genes."""
    # Extract config parameters
    gene_coefficient = config.gene_coefficient
    weight_coefficient = config.weight_coefficient
    
    # Extract innovation numbers and weights
    innov1, innov2 = connections1[..., 3], connections2[..., 3]
    weights1, weights2 = connections1[..., 2], connections2[..., 2]

    # Sort innovations and corresponding data
    idx1, idx2 = jnp.argsort(innov1), jnp.argsort(innov2)
    innov1_sorted, innov2_sorted = innov1[idx1], innov2[idx2]
    weights1_sorted, weights2_sorted = weights1[idx1], weights2[idx2]
    enabled1_sorted, enabled2_sorted = enabled1[idx1], enabled2[idx2]

    # Find matching genes
    matched_idx = jnp.searchsorted(innov2_sorted, innov1_sorted)
    valid_mask = matched_idx < innov2_sorted.shape[0]
    match_mask = valid_mask & (innov1_sorted == jnp.where(valid_mask,
                                                         innov2_sorted[matched_idx],
                                                         innov1_sorted))
    enabled_mask = match_mask & enabled1_sorted & enabled2_sorted[matched_idx]

    # Calculate gene differences (both excess and disjoint) considering enabled status
    gene_diff_mask = ~match_mask & (enabled1_sorted | enabled2_sorted[matched_idx])
    gene_diff = jnp.sum(gene_diff_mask & enabled1_sorted)  # Count only enabled genes from genome1
    gene_diff += jnp.sum(~match_mask & enabled2_sorted)    # Add enabled non-matching genes from genome2

    # Compute average weight difference for matching enabled genes
    weight_diff = jnp.sum(jnp.where(enabled_mask,
                                   jnp.abs(weights1_sorted - weights2_sorted[matched_idx]),
                                   0.0))
    matching_count = jnp.maximum(jnp.sum(enabled_mask), 1.0)

    return gene_coefficient * gene_diff + weight_coefficient * (weight_diff / matching_count)

def compute_pairwise_distances(
    connections1: jnp.ndarray,
    enabled1: jnp.ndarray,
    connections2: jnp.ndarray,
    enabled2: jnp.ndarray,
    config: SpeciesConfig
) -> jnp.ndarray:
    distance_fn = lambda g1, g2: compute_compatibility_distance(
        connections1=g1,
        enabled1=enabled1,
        connections2=g2,
        enabled2=enabled2,
        config=config
    )
    return jax.vmap(jax.vmap(distance_fn, (None, 0)), (0, None))(connections1, connections2)

@partial(jax.jit, static_argnames=['config'])
def assign_species(
    state: SpeciesState,
    connections: jnp.ndarray,
    enabled: jnp.ndarray,
    config: SpeciesConfig
) -> SpeciesState:
    """Assign genomes to species based on compatibility distance."""
    # Extract config parameters
    compatibility_threshold = config.compatibility_threshold
    
    pop_size = connections.shape[0]
    assigned = jnp.zeros(pop_size, dtype=jnp.bool_)
    
    def scan_step(carry, idx):
        state, assigned = carry
        
        # Find compatible species
        distances = jnp.where(
            state.active_mask,
            compute_pairwise_distances(
                jnp.expand_dims(connections[idx], 0),
                jnp.expand_dims(enabled[idx], 0),
                state.representatives,
                enabled,
                config
            ),
            jnp.inf
        )
        
        # Assign to existing species or create new one
        compatible = distances < compatibility_threshold
        # Check if we can create a new species (haven't reached max_species limit)
        can_create_new = state.next_species_id < config.max_species
        
        # If no compatible species found, check if we can create new species
        # If we can't create new (reached limit), force assignment to closest existing species
        has_active_species = jnp.any(state.active_mask)
        closest_species = jnp.where(has_active_species, jnp.argmin(distances), 0)
        
        species_id = jnp.where(
            jnp.any(compatible), 
            jnp.argmin(distances), 
            jnp.where(can_create_new, state.next_species_id, closest_species)
        )
        
        # Add validity check for new representatives
        valid_representative = jnp.where(
            jnp.any(enabled[idx]),  # Prevent empty genomes as representatives
            connections[idx],
            state.representatives[species_id]  # Keep previous representative if invalid
        )
        
        # Only increment next_species_id if we actually created a new species
        creating_new_species = ~jnp.any(compatible) & can_create_new
        new_state = state.replace(
            species_ids=state.species_ids.at[idx].set(species_id),
            next_species_id=jnp.where(creating_new_species, 
                                      state.next_species_id + 1, 
                                      state.next_species_id),
            active_mask=state.active_mask.at[species_id].set(True),
            member_masks=state.member_masks.at[species_id, idx].set(True),
            representatives=jnp.where(jnp.any(compatible),
                                   state.representatives,
                                   state.representatives.at[species_id].set(valid_representative))
        )
        return (new_state, assigned.at[idx].set(True)), None

    (final_state, _), _ = jax.lax.scan(scan_step, (state, assigned), jnp.arange(pop_size))
    return final_state

@partial(jax.jit, static_argnames=['config'])
def allocate_offspring(
    state: SpeciesState,
    fitness: jnp.ndarray,
    config: SpeciesConfig
) -> SpeciesState:
    """Allocate offspring based on species fitness and rank."""
    # Extract config parameters
    stagnation_threshold = config.stagnation_threshold
    rank_strategy = config.rank_strategy
    
    pop_size = fitness.shape[0]
    max_species = state.member_masks.shape[0]
    
    # Compute species scores
    species_scores = jnp.max(jnp.where(
        state.member_masks[:max_species, :pop_size],
        jnp.expand_dims(fitness, 0),
        -jnp.inf
    ), axis=1)
    
    # Update best fitnesses and improvement tracking
    new_best_fitnesses = jnp.maximum(state.best_fitnesses, species_scores)
    improved = species_scores > state.best_fitnesses
    raw_last_improvements = jnp.where(improved, 0, state.last_improvements + 1)
    # Set to -1 if inactive
    new_last_improvements = jnp.where(state.active_mask, raw_last_improvements, -1)
    
    # Apply stagnation threshold
    stagnant = new_last_improvements > stagnation_threshold
    
    # Apply size-aware scoring and ranking
    species_sizes = jnp.sum(state.member_masks[:max_species, :pop_size], axis=1)
    size_aware_scores = species_scores * jnp.log1p(species_sizes)
    ranks = jnp.zeros_like(size_aware_scores).at[
        jnp.argsort(jnp.argsort(-size_aware_scores))
    ].set(jnp.arange(len(size_aware_scores)))
    
    # Calculate offspring shares
    shares = jnp.where(
        rank_strategy == 1,  # 1 = exponential, 0 = linear
        1.0 / (ranks + 1.0),
        jnp.maximum(0.0, jnp.sum(state.active_mask) - ranks)
    )
    
    # Apply stagnation and ensure best species survival
    # Also ensure species with no members get 0 shares
    has_members = species_sizes > 0
    shares *= (state.active_mask & ~stagnant & has_members)
    
    # Only protect best species if it has members and is not stagnant
    best_idx = jnp.argmax(species_scores)
    best_species_valid = has_members[best_idx] & state.active_mask[best_idx] & ~stagnant[best_idx]
    shares = shares.at[best_idx].set(jnp.where(
        (jnp.sum(shares) > 0) | ~best_species_valid, 
        shares[best_idx], 
        1.0
    ))
    
    # Convert shares to offspring counts
    total_shares = jnp.maximum(jnp.sum(shares), 1e-12)
    offspring = (shares / total_shares * pop_size).astype(jnp.float32)
    offspring = offspring.at[best_idx].add(pop_size - jnp.sum(offspring))
    
    # Update active_mask to immediately deactivate stagnant species
    active_mask = (offspring > 0) & ~stagnant
    
    return state.replace(
        offspring_counts=offspring,
        active_mask=active_mask,
        best_fitnesses=new_best_fitnesses,
        last_improvements=new_last_improvements
    )
    
def update_species(
    state: SpeciesState,
    connections: jnp.ndarray,
    enabled: jnp.ndarray,
    fitness: jnp.ndarray,
    config: SpeciesConfig
) -> SpeciesState:
    """Update species assignments and allocate offspring."""
    state = assign_species(state, connections, enabled, config)
    return allocate_offspring(state, fitness, config)